"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/applications/page",{

/***/ "(app-pages-browser)/./src/app/applications/page.tsx":
/*!***************************************!*\
  !*** ./src/app/applications/page.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Card,CardContent,Chip,Grid,LinearProgress,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Assignment_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Assignment!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Assignment_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Assignment!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _components_DummyDataProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DummyDataProvider */ \"(app-pages-browser)/./src/components/DummyDataProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst SimpleApplicationsPage = ()=>{\n    const applications = _components_DummyDataProvider__WEBPACK_IMPORTED_MODULE_2__.mockData.applications;\n    const getStatusColor = (status)=>{\n        const colors = {\n            new: \"info\",\n            in_review: \"primary\",\n            documents_pending: \"warning\",\n            interview_scheduled: \"secondary\",\n            interviewed: \"secondary\",\n            shortlisted: \"primary\",\n            offered: \"success\",\n            accepted: \"success\",\n            enrolled: \"success\",\n            rejected: \"error\",\n            withdrawn: \"default\",\n            waitlisted: \"warning\"\n        };\n        return colors[status] || \"default\";\n    };\n    const getApplicationProgress = (status)=>{\n        const progressMap = {\n            new: 10,\n            in_review: 25,\n            documents_pending: 35,\n            interview_scheduled: 50,\n            interviewed: 65,\n            shortlisted: 75,\n            offered: 85,\n            accepted: 95,\n            enrolled: 100,\n            rejected: 100,\n            withdrawn: 100,\n            waitlisted: 70\n        };\n        return progressMap[status] || 0;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"h4\",\n                                gutterBottom: true,\n                                children: \"Applications Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Track and manage student applications through the admission process.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"contained\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Assignment_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: ()=>alert(\"New application functionality would be implemented here\"),\n                        children: \"New Application\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Assignment_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: \"primary\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"primary\",\n                                                    children: applications.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Total Applications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Assignment_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: \"info\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"info.main\",\n                                                    children: applications.filter((a)=>a.status === \"in_review\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Under Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Assignment_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: \"success\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"success.main\",\n                                                    children: applications.filter((a)=>a.status === \"offered\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Offers Made\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Assignment_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: \"warning\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"warning.main\",\n                                                    children: applications.filter((a)=>a.status === \"enrolled\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Enrolled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            variant: \"h6\",\n                            gutterBottom: true,\n                            children: \"All Applications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            component: _barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                            variant: \"outlined\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Application #\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Applicant Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Program\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Merit Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Submitted\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: applications.map((application)=>{\n                                            var _application_academic_programs;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                hover: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            fontWeight: \"bold\",\n                                                            color: \"primary\",\n                                                            children: application.application_number\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            children: [\n                                                                application.first_name,\n                                                                \" \",\n                                                                application.last_name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            children: (_application_academic_programs = application.academic_programs) === null || _application_academic_programs === void 0 ? void 0 : _application_academic_programs.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            label: application.status.replace(\"_\", \" \").toUpperCase(),\n                                                            color: getStatusColor(application.status),\n                                                            size: \"small\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            sx: {\n                                                                width: \"100%\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    variant: \"determinate\",\n                                                                    value: getApplicationProgress(application.status),\n                                                                    sx: {\n                                                                        height: 8,\n                                                                        borderRadius: 4\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    color: \"text.secondary\",\n                                                                    children: [\n                                                                        getApplicationProgress(application.status),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            fontWeight: \"bold\",\n                                                            children: application.merit_score ? \"\".concat(application.merit_score, \"/100\") : \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            children: new Date(application.submission_date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                gap: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    variant: \"outlined\",\n                                                                    onClick: ()=>alert(\"View application \".concat(application.application_number)),\n                                                                    children: \"View\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    size: \"small\",\n                                                                    variant: \"outlined\",\n                                                                    onClick: ()=>alert(\"Edit application \".concat(application.application_number)),\n                                                                    children: \"Edit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, application.id, true, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                sx: {\n                    mt: 3,\n                    p: 2,\n                    bgcolor: \"info.light\",\n                    borderRadius: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Card_CardContent_Chip_Grid_LinearProgress_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    variant: \"body2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Demo Mode:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        \" This page displays dummy data for demonstration purposes. In a real implementation, this would connect to your database and allow full CRUD operations.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\applications\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleApplicationsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SimpleApplicationsPage);\nvar _c;\n$RefreshReg$(_c, \"SimpleApplicationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/applications/page.tsx\n"));

/***/ })

});