/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/communications/page";
exports.ids = ["app/communications/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunications%2Fpage&page=%2Fcommunications%2Fpage&appPaths=%2Fcommunications%2Fpage&pagePath=private-next-app-dir%2Fcommunications%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunications%2Fpage&page=%2Fcommunications%2Fpage&appPaths=%2Fcommunications%2Fpage&pagePath=private-next-app-dir%2Fcommunications%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'communications',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/communications/page.tsx */ \"(rsc)/./src/app/communications/page.tsx\")), \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/communications/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/communications/page\",\n        pathname: \"/communications\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunications%2Fpage&page=%2Fcommunications%2Fpage&appPaths=%2Fcommunications%2Fpage&pagePath=private-next-app-dir%2Fcommunications%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ccore%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22GitHubBanner%22%2C%22Refine%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ckbar%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineKbarProvider%22%2C%22RefineKbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cmui%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineSnackbarProvider%22%2C%22useNotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cnextjs-router%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccontexts%5C%5Ccolor-mode%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22ColorModeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider%5C%5Cauth-provider.client.ts%22%2C%22ids%22%3A%5B%22authProviderClient%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdata-provider%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22dataProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdevtools%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22DevtoolsProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ccore%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22GitHubBanner%22%2C%22Refine%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ckbar%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineKbarProvider%22%2C%22RefineKbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cmui%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineSnackbarProvider%22%2C%22useNotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cnextjs-router%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccontexts%5C%5Ccolor-mode%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22ColorModeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider%5C%5Cauth-provider.client.ts%22%2C%22ids%22%3A%5B%22authProviderClient%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdata-provider%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22dataProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdevtools%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22DevtoolsProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/core/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/core/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/kbar/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/kbar/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/mui/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/nextjs-router/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/nextjs-router/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/color-mode/index.tsx */ \"(ssr)/./src/contexts/color-mode/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider/auth-provider.client.ts */ \"(ssr)/./src/providers/auth-provider/auth-provider.client.ts\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/data-provider/index.ts */ \"(ssr)/./src/providers/data-provider/index.ts\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/devtools/index.tsx */ \"(ssr)/./src/providers/devtools/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ccore%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22GitHubBanner%22%2C%22Refine%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ckbar%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineKbarProvider%22%2C%22RefineKbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cmui%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineSnackbarProvider%22%2C%22useNotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cnextjs-router%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccontexts%5C%5Ccolor-mode%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22ColorModeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider%5C%5Cauth-provider.client.ts%22%2C%22ids%22%3A%5B%22authProviderClient%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdata-provider%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22dataProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdevtools%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22DevtoolsProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDY2l0cnVzLXdvcmtzJTVDJTVDQ01TJTVDJTVDc3R1ZGVudC1hZG1pc3Npb25zLWNtcyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q2NpdHJ1cy13b3JrcyU1QyU1Q0NNUyU1QyU1Q3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQStJO0FBQy9JO0FBQ0Esb09BQWdKO0FBQ2hKO0FBQ0EsME9BQW1KO0FBQ25KO0FBQ0Esd09BQWtKO0FBQ2xKO0FBQ0Esa1BBQXVKO0FBQ3ZKO0FBQ0Esc1FBQWlLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8/NjBhYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGNpdHJ1cy13b3Jrc1xcXFxDTVNcXFxcc3R1ZGVudC1hZG1pc3Npb25zLWNtc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGNpdHJ1cy13b3Jrc1xcXFxDTVNcXFxcc3R1ZGVudC1hZG1pc3Npb25zLWNtc1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxjaXRydXMtd29ya3NcXFxcQ01TXFxcXHN0dWRlbnQtYWRtaXNzaW9ucy1jbXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY2l0cnVzLXdvcmtzXFxcXENNU1xcXFxzdHVkZW50LWFkbWlzc2lvbnMtY21zXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcY2l0cnVzLXdvcmtzXFxcXENNU1xcXFxzdHVkZW50LWFkbWlzc2lvbnMtY21zXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxjaXRydXMtd29ya3NcXFxcQ01TXFxcXHN0dWRlbnQtYWRtaXNzaW9ucy1jbXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Ccommunications%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Ccommunications%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/communications/page.tsx */ \"(ssr)/./src/app/communications/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDY29tbXVuaWNhdGlvbnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEtBQXNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8/NThlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGNpdHJ1cy13b3Jrc1xcXFxDTVNcXFxcc3R1ZGVudC1hZG1pc3Npb25zLWNtc1xcXFxzcmNcXFxcYXBwXFxcXGNvbW11bmljYXRpb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Ccommunications%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbm90LWZvdW5kLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQTJHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8/Njg3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGNpdHJ1cy13b3Jrc1xcXFxDTVNcXFxcc3R1ZGVudC1hZG1pc3Npb25zLWNtc1xcXFxzcmNcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/communications/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/communications/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Email.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Sms.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/WhatsApp.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Article.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Preview.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Edit.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction TabPanel(props) {\n    const { children, value, index, ...other } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        role: \"tabpanel\",\n        hidden: value !== index,\n        id: `simple-tabpanel-${index}`,\n        \"aria-labelledby\": `simple-tab-${index}`,\n        ...other,\n        children: value === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n            lineNumber: 55,\n            columnNumber: 27\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\nconst CommunicationsPage = ()=>{\n    const [tabValue, setTabValue] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    // Enhanced dummy data for communications\n    const templates = [\n        {\n            id: \"1\",\n            name: \"Application Received\",\n            type: \"email\",\n            subject: \"Your Application Has Been Received - {{application_number}}\",\n            content: \"Dear {{first_name}}, Thank you for submitting your application for {{program_name}}.\",\n            variables: [\n                \"first_name\",\n                \"application_number\",\n                \"program_name\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-15T10:00:00Z\",\n            usage_count: 45\n        },\n        {\n            id: \"2\",\n            name: \"Interview Invitation\",\n            type: \"email\",\n            subject: \"Interview Invitation - {{program_name}}\",\n            content: \"Dear {{first_name}}, You are invited for an interview on {{interview_date}}.\",\n            variables: [\n                \"first_name\",\n                \"program_name\",\n                \"interview_date\",\n                \"interview_time\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-10T14:30:00Z\",\n            usage_count: 23\n        },\n        {\n            id: \"3\",\n            name: \"Admission Offer\",\n            type: \"email\",\n            subject: \"Congratulations! Admission Offer - {{program_name}}\",\n            content: \"Dear {{first_name}}, Congratulations! You have been offered admission to {{program_name}}.\",\n            variables: [\n                \"first_name\",\n                \"program_name\",\n                \"offer_deadline\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-05T09:15:00Z\",\n            usage_count: 67\n        },\n        {\n            id: \"4\",\n            name: \"Document Reminder\",\n            type: \"sms\",\n            subject: \"\",\n            content: \"Hi {{first_name}}, Please submit your pending documents for application {{application_number}}.\",\n            variables: [\n                \"first_name\",\n                \"application_number\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-20T16:45:00Z\",\n            usage_count: 12\n        },\n        {\n            id: \"5\",\n            name: \"Fee Payment Reminder\",\n            type: \"whatsapp\",\n            subject: \"\",\n            content: \"Hello {{first_name}}, Your fee payment of ${{amount}} is due by {{due_date}}.\",\n            variables: [\n                \"first_name\",\n                \"amount\",\n                \"due_date\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-18T11:20:00Z\",\n            usage_count: 34\n        }\n    ];\n    const notifications = [\n        {\n            id: \"1\",\n            template_name: \"Application Received\",\n            recipient: \"<EMAIL>\",\n            type: \"email\",\n            status: \"delivered\",\n            sent_at: \"2024-01-22T10:30:00Z\",\n            delivered_at: \"2024-01-22T10:31:15Z\"\n        },\n        {\n            id: \"2\",\n            template_name: \"Interview Invitation\",\n            recipient: \"<EMAIL>\",\n            type: \"email\",\n            status: \"delivered\",\n            sent_at: \"2024-01-22T09:15:00Z\",\n            delivered_at: \"2024-01-22T09:16:22Z\"\n        },\n        {\n            id: \"3\",\n            template_name: \"Document Reminder\",\n            recipient: \"******-0123\",\n            type: \"sms\",\n            status: \"failed\",\n            sent_at: \"2024-01-22T08:45:00Z\",\n            error_message: \"Invalid phone number\"\n        },\n        {\n            id: \"4\",\n            template_name: \"Admission Offer\",\n            recipient: \"<EMAIL>\",\n            type: \"email\",\n            status: \"pending\",\n            sent_at: \"2024-01-22T11:00:00Z\"\n        }\n    ];\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"email\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 16\n                }, undefined);\n            case \"sms\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 16\n                }, undefined);\n            case \"whatsapp\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTypeColor = (type)=>{\n        const colors = {\n            email: \"primary\",\n            sms: \"success\",\n            whatsapp: \"info\",\n            in_app: \"secondary\"\n        };\n        return colors[type] || \"default\";\n    };\n    const getStatusColor = (status)=>{\n        const colors = {\n            delivered: \"success\",\n            pending: \"warning\",\n            failed: \"error\",\n            sent: \"info\"\n        };\n        return colors[status] || \"default\";\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"delivered\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 16\n                }, undefined);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 16\n                }, undefined);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h4\",\n                                gutterBottom: true,\n                                children: \"Communication Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Manage communication templates and send notifications to applicants and students.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"contained\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: ()=>alert(\"Create new template functionality would be implemented here\"),\n                        children: \"New Template\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            color: \"primary\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"primary\",\n                                                    children: templates.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Active Templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            color: \"info\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"info.main\",\n                                                    children: notifications.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Sent Today\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            color: \"success\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"success.main\",\n                                                    children: notifications.filter((n)=>n.status === \"delivered\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Delivered\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            color: \"error\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"error.main\",\n                                                    children: notifications.filter((n)=>n.status === \"failed\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Failed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    borderBottom: 1,\n                    borderColor: \"divider\",\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    value: tabValue,\n                    onChange: (_, newValue)=>setTabValue(newValue),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            label: \"Templates\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            label: \"Send Notifications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            label: \"Notification History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                value: tabValue,\n                index: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h6\",\n                                children: \"Communication Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: ()=>alert(\"Create new template functionality would be implemented here\"),\n                                children: \"New Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        component: _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        variant: \"outlined\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Template Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Subject\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Variables\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Usage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        fontWeight: \"bold\",\n                                                        children: template.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        icon: getTypeIcon(template.type),\n                                                        label: template.type.toUpperCase(),\n                                                        color: getTypeColor(template.type),\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        noWrap: true,\n                                                        children: template.type === \"email\" ? template.subject : \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        color: \"text.secondary\",\n                                                        children: [\n                                                            template.variables?.length || 0,\n                                                            \" variables\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        label: template.is_active ? \"Active\" : \"Inactive\",\n                                                        color: template.is_active ? \"success\" : \"default\",\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        color: \"primary\",\n                                                        children: [\n                                                            template.usage_count,\n                                                            \" times\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            gap: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                size: \"small\",\n                                                                variant: \"outlined\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                onClick: ()=>alert(`Preview template: ${template.name}`),\n                                                                children: \"Preview\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                size: \"small\",\n                                                                variant: \"outlined\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                onClick: ()=>alert(`Edit template: ${template.name}`),\n                                                                children: \"Edit\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, template.id, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                value: tabValue,\n                index: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Send Bulk Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                gutterBottom: true,\n                                                children: \"Send notifications to multiple recipients at once\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"contained\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 30\n                                                }, void 0),\n                                                fullWidth: true,\n                                                onClick: ()=>alert(\"Bulk notification functionality would be implemented here\"),\n                                                children: \"Send Bulk Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Quick Send\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                gutterBottom: true,\n                                                children: \"Send individual notifications using templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 30\n                                                }, void 0),\n                                                fullWidth: true,\n                                                onClick: ()=>alert(\"Quick send functionality would be implemented here\"),\n                                                children: \"Quick Send\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        severity: \"info\",\n                        sx: {\n                            mt: 3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h6\",\n                                gutterBottom: true,\n                                children: \"Bulk Notification Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"body2\",\n                                children: \"The bulk notification system would include:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Recipient group selection (all applicants, specific status, program, etc.)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Template selection with variable substitution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Preview before sending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Scheduled sending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Delivery tracking and analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                value: tabValue,\n                index: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Recent Notifications\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        component: _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        variant: \"outlined\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Recipient\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Sent At\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Delivered At\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        fontWeight: \"bold\",\n                                                        children: notification.template_name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: notification.recipient\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        icon: getTypeIcon(notification.type),\n                                                        label: notification.type.toUpperCase(),\n                                                        color: getTypeColor(notification.type),\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        icon: getStatusIcon(notification.status),\n                                                        label: notification.status.toUpperCase(),\n                                                        color: getStatusColor(notification.status),\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: new Date(notification.sent_at).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: notification.delivered_at ? new Date(notification.delivered_at).toLocaleString() : notification.error_message || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, notification.id, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    mt: 3,\n                    p: 2,\n                    bgcolor: \"info.light\",\n                    borderRadius: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Demo Mode:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, undefined),\n                            \" This page displays dummy data for demonstration purposes. In a real implementation, this would include:\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Rich text editor for template creation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Variable insertion and preview functionality\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Integration with email/SMS/WhatsApp providers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Automated trigger-based notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"A/B testing for templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Comprehensive delivery and engagement analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 525,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommunicationsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/communications/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @refinedev/mui */ \"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* harmony import */ var _refinedev_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @refinedev/core */ \"(ssr)/./node_modules/@refinedev/core/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_core__WEBPACK_IMPORTED_MODULE_2__.Authenticated, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_mui__WEBPACK_IMPORTED_MODULE_3__.ErrorComponent, {}, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 11,\n                columnNumber: 17\n            }, this)\n        }, \"not-found\", false, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDb0I7QUFDTDtBQUVoQyxTQUFTRztJQUNwQixxQkFDSSw4REFBQ0gsMkNBQVFBO2tCQUNMLDRFQUFDRSwwREFBYUE7c0JBQ1YsNEVBQUNELDBEQUFjQTs7Ozs7V0FEQTs7Ozs7Ozs7OztBQUsvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9zcmMvYXBwL25vdC1mb3VuZC50c3g/Y2FlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyBTdXNwZW5zZSB9IGZyb20gJ3JlYWN0J1xuICAgIGltcG9ydCB7IEVycm9yQ29tcG9uZW50IH0gZnJvbSBcIkByZWZpbmVkZXYvbXVpXCI7XG5pbXBvcnQgeyBBdXRoZW50aWNhdGVkIH0gZnJvbSAnQHJlZmluZWRldi9jb3JlJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgICByZXR1cm4gKFxuICAgICAgICA8U3VzcGVuc2U+XG4gICAgICAgICAgICA8QXV0aGVudGljYXRlZCBrZXk9J25vdC1mb3VuZCc+XG4gICAgICAgICAgICAgICAgPEVycm9yQ29tcG9uZW50IC8+XG4gICAgICAgICAgICA8L0F1dGhlbnRpY2F0ZWQ+XG4gICAgICAgIDwvU3VzcGVuc2U+XG4gICAgKVxufVxuIl0sIm5hbWVzIjpbIlN1c3BlbnNlIiwiRXJyb3JDb21wb25lbnQiLCJBdXRoZW50aWNhdGVkIiwiTm90Rm91bmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/color-mode/index.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/color-mode/index.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorModeContext: () => (/* binding */ ColorModeContext),\n/* harmony export */   ColorModeContextProvider: () => (/* binding */ ColorModeContextProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"(ssr)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @refinedev/mui */ \"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _mui_material_GlobalStyles__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/GlobalStyles */ \"(ssr)/./node_modules/@mui/material/GlobalStyles/GlobalStyles.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* __next_internal_client_entry_do_not_use__ ColorModeContext,ColorModeContextProvider auto */ \n\n\n\n\n\n\n\nconst ColorModeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst ColorModeContextProvider = ({ children, defaultMode })=>{\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultMode || \"light\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    const systemTheme = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(`(prefers-color-scheme: dark)`);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMounted) {\n            const theme = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"theme\") || (systemTheme ? \"dark\" : \"light\");\n            setMode(theme);\n        }\n    }, [\n        isMounted,\n        systemTheme\n    ]);\n    const toggleTheme = ()=>{\n        const nextTheme = mode === \"light\" ? \"dark\" : \"light\";\n        setMode(nextTheme);\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"theme\", nextTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorModeContext.Provider, {\n        value: {\n            setMode: toggleTheme,\n            mode\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            // you can change the theme colors here. example: mode === \"light\" ? RefineThemes.Magenta : RefineThemes.MagentaDark\n            theme: mode === \"light\" ? _refinedev_mui__WEBPACK_IMPORTED_MODULE_5__.RefineThemes.Blue : _refinedev_mui__WEBPACK_IMPORTED_MODULE_5__.RefineThemes.BlueDark,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GlobalStyles__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    styles: {\n                        html: {\n                            WebkitFontSmoothing: \"auto\"\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 17\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n            lineNumber: 62,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n        lineNumber: 56,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/color-mode/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/auth-provider/auth-provider.client.ts":
/*!*************************************************************!*\
  !*** ./src/providers/auth-provider/auth-provider.client.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authProviderClient: () => (/* binding */ authProviderClient)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ authProviderClient auto */ \nconst authProviderClient = {\n    login: async ({ email, password })=>{\n        const { data, error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            return {\n                success: false,\n                error\n            };\n        }\n        if (data?.session) {\n            await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.setSession(data.session);\n            return {\n                success: true,\n                redirectTo: \"/\"\n            };\n        }\n        // for third-party login\n        return {\n            success: false,\n            error: {\n                name: \"LoginError\",\n                message: \"Invalid username or password\"\n            }\n        };\n    },\n    logout: async ()=>{\n        const { error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.signOut();\n        if (error) {\n            return {\n                success: false,\n                error\n            };\n        }\n        return {\n            success: true,\n            redirectTo: \"/login\"\n        };\n    },\n    register: async ({ email, password, fullName, role = \"applicant\" })=>{\n        try {\n            const { data, error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: fullName,\n                        role: role\n                    }\n                }\n            });\n            if (error) {\n                return {\n                    success: false,\n                    error\n                };\n            }\n            if (data?.user) {\n                // Create user profile in public.users table\n                const { error: profileError } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.from(\"users\").insert({\n                    id: data.user.id,\n                    email: data.user.email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"Error creating user profile:\", profileError);\n                }\n                return {\n                    success: true,\n                    redirectTo: role === \"applicant\" ? \"/applications\" : \"/dashboard\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error\n            };\n        }\n        return {\n            success: false,\n            error: {\n                message: \"Register failed\",\n                name: \"Invalid email or password\"\n            }\n        };\n    },\n    check: async ()=>{\n        const { data, error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.getUser();\n        const { user } = data;\n        if (error) {\n            return {\n                authenticated: false,\n                redirectTo: \"/login\",\n                logout: true\n            };\n        }\n        if (user) {\n            return {\n                authenticated: true\n            };\n        }\n        return {\n            authenticated: false,\n            redirectTo: \"/login\"\n        };\n    },\n    getPermissions: async ()=>{\n        const { data } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.getUser();\n        if (data?.user) {\n            // Get user role from public.users table\n            const { data: userProfile } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.from(\"users\").select(\"role\").eq(\"id\", data.user.id).single();\n            return userProfile?.role || \"applicant\";\n        }\n        return null;\n    },\n    getIdentity: async ()=>{\n        const { data } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.getUser();\n        if (data?.user) {\n            // Get full user profile from public.users table\n            const { data: userProfile } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.from(\"users\").select(\"*\").eq(\"id\", data.user.id).single();\n            return {\n                id: data.user.id,\n                email: data.user.email,\n                name: userProfile?.full_name || data.user.email,\n                avatar: userProfile?.avatar_url,\n                role: userProfile?.role || \"applicant\",\n                phone: userProfile?.phone,\n                isActive: userProfile?.is_active\n            };\n        }\n        return null;\n    },\n    onError: async (error)=>{\n        if (error?.code === \"PGRST301\" || error?.code === 401) {\n            return {\n                logout: true\n            };\n        }\n        return {\n            error\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/auth-provider/auth-provider.client.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/data-provider/index.ts":
/*!**********************************************!*\
  !*** ./src/providers/data-provider/index.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataProvider: () => (/* binding */ dataProvider)\n/* harmony export */ });\n/* harmony import */ var _refinedev_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @refinedev/supabase */ \"(ssr)/./node_modules/@refinedev/supabase/dist/index.mjs\");\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ dataProvider auto */ \n\nconst dataProvider = (0,_refinedev_supabase__WEBPACK_IMPORTED_MODULE_0__.dataProvider)(_utils_supabase_client__WEBPACK_IMPORTED_MODULE_1__.supabaseBrowserClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL2RhdGEtcHJvdmlkZXIvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O2tFQUUyRTtBQUNaO0FBRXhELE1BQU1BLGVBQWVDLGlFQUFvQkEsQ0FBQ0MseUVBQXFCQSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL3NyYy9wcm92aWRlcnMvZGF0YS1wcm92aWRlci9pbmRleC50cz8zYTIzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBkYXRhUHJvdmlkZXIgYXMgZGF0YVByb3ZpZGVyU3VwYWJhc2UgfSBmcm9tIFwiQHJlZmluZWRldi9zdXBhYmFzZVwiO1xuaW1wb3J0IHsgc3VwYWJhc2VCcm93c2VyQ2xpZW50IH0gZnJvbSBcIkB1dGlscy9zdXBhYmFzZS9jbGllbnRcIjtcblxuZXhwb3J0IGNvbnN0IGRhdGFQcm92aWRlciA9IGRhdGFQcm92aWRlclN1cGFiYXNlKHN1cGFiYXNlQnJvd3NlckNsaWVudCk7XG4iXSwibmFtZXMiOlsiZGF0YVByb3ZpZGVyIiwiZGF0YVByb3ZpZGVyU3VwYWJhc2UiLCJzdXBhYmFzZUJyb3dzZXJDbGllbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/data-provider/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/devtools/index.tsx":
/*!******************************************!*\
  !*** ./src/providers/devtools/index.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DevtoolsProvider: () => (/* binding */ DevtoolsProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _refinedev_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @refinedev/devtools */ \"(ssr)/./node_modules/@refinedev/devtools/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ DevtoolsProvider auto */ \n\n\nconst DevtoolsProvider = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_devtools__WEBPACK_IMPORTED_MODULE_2__.DevtoolsProvider, {\n        children: [\n            props.children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_devtools__WEBPACK_IMPORTED_MODULE_2__.DevtoolsPanel, {}, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\providers\\\\devtools\\\\index.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\providers\\\\devtools\\\\index.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL2RldnRvb2xzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXlCO0FBQ29FO0FBRXRGLE1BQU1FLG1CQUFtQixDQUFDRTtJQUMvQixxQkFDRSw4REFBQ0QsaUVBQW9CQTs7WUFDbEJDLE1BQU1DLFFBQVE7MEJBQ2YsOERBQUNKLDhEQUFhQTs7Ozs7Ozs7Ozs7QUFHcEIsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9zcmMvcHJvdmlkZXJzL2RldnRvb2xzL2luZGV4LnRzeD9jMzRmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBEZXZ0b29sc1BhbmVsLCBEZXZ0b29sc1Byb3ZpZGVyIGFzIERldnRvb2xzUHJvdmlkZXJCYXNlIH0gZnJvbSAnQHJlZmluZWRldi9kZXZ0b29scydcblxuZXhwb3J0IGNvbnN0IERldnRvb2xzUHJvdmlkZXIgPSAocHJvcHM6IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPERldnRvb2xzUHJvdmlkZXJCYXNlPlxuICAgICAge3Byb3BzLmNoaWxkcmVufVxuICAgICAgPERldnRvb2xzUGFuZWwgLz5cbiAgICA8L0RldnRvb2xzUHJvdmlkZXJCYXNlPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJEZXZ0b29sc1BhbmVsIiwiRGV2dG9vbHNQcm92aWRlciIsIkRldnRvb2xzUHJvdmlkZXJCYXNlIiwicHJvcHMiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/devtools/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/supabase/client.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/client.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabaseBrowserClient: () => (/* binding */ supabaseBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/utils/supabase/constants.ts\");\n\n\nconst supabaseBrowserClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(_constants__WEBPACK_IMPORTED_MODULE_1__.SUPABASE_URL, _constants__WEBPACK_IMPORTED_MODULE_1__.SUPABASE_KEY, {\n    db: {\n        schema: \"public\"\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUNLO0FBRWxELE1BQU1HLHdCQUF3Qkgsa0VBQW1CQSxDQUNwREUsb0RBQVlBLEVBQ1pELG9EQUFZQSxFQUNaO0lBQ0lHLElBQUk7UUFDQUMsUUFBUTtJQUNaO0FBQ0osR0FDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzPzcxYTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQnJvd3NlckNsaWVudCB9IGZyb20gXCJAc3VwYWJhc2Uvc3NyXCI7XG5pbXBvcnQgeyBTVVBBQkFTRV9LRVksIFNVUEFCQVNFX1VSTCB9IGZyb20gXCIuL2NvbnN0YW50c1wiO1xuXG5leHBvcnQgY29uc3Qgc3VwYWJhc2VCcm93c2VyQ2xpZW50ID0gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBTVVBBQkFTRV9VUkwsXG4gICAgU1VQQUJBU0VfS0VZLFxuICAgIHtcbiAgICAgICAgZGI6IHtcbiAgICAgICAgICAgIHNjaGVtYTogXCJwdWJsaWNcIixcbiAgICAgICAgfSxcbiAgICB9LFxuKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiU1VQQUJBU0VfS0VZIiwiU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VCcm93c2VyQ2xpZW50IiwiZGIiLCJzY2hlbWEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/supabase/constants.ts":
/*!*****************************************!*\
  !*** ./src/utils/supabase/constants.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPABASE_KEY: () => (/* binding */ SUPABASE_KEY),\n/* harmony export */   SUPABASE_URL: () => (/* binding */ SUPABASE_URL)\n/* harmony export */ });\nconst SUPABASE_URL = \"https://iwdfzvfqbtokqetmbmbp.supabase.co\";\nconst SUPABASE_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlhdCI6MTYzMDU2NzAxMCwiZXhwIjoxOTQ2MTQzMDEwfQ._gr6kXGkQBi9BM9dx5vKaNKYj_DJN1xlkarprGpM_fU\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY29uc3RhbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sTUFBTUEsZUFBZSwyQ0FBMkM7QUFDaEUsTUFBTUMsZUFDVCxzSkFBc0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vc3JjL3V0aWxzL3N1cGFiYXNlL2NvbnN0YW50cy50cz81MDY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBTVVBBQkFTRV9VUkwgPSBcImh0dHBzOi8vaXdkZnp2ZnFidG9rcWV0bWJtYnAuc3VwYWJhc2UuY29cIjtcbmV4cG9ydCBjb25zdCBTVVBBQkFTRV9LRVkgPVxuICAgIFwiZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnliMnhsSWpvaVlXNXZiaUlzSW1saGRDSTZNVFl6TURVMk56QXhNQ3dpWlhod0lqb3hPVFEyTVRRek1ERXdmUS5fZ3I2a1hHa1FCaTlCTTlkeDV2S2FOS1lqX0RKTjF4bGthcnByR3BNX2ZVXCI7XG4iXSwibmFtZXMiOlsiU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfS0VZIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/supabase/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/communications/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/communications/page.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\app\communications\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _refinedev_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @refinedev/core */ \"(rsc)/./node_modules/@refinedev/core/dist/index.mjs\");\n/* harmony import */ var _providers_devtools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @providers/devtools */ \"(rsc)/./src/providers/devtools/index.tsx\");\n/* harmony import */ var _refinedev_kbar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @refinedev/kbar */ \"(rsc)/./node_modules/@refinedev/kbar/dist/index.mjs\");\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @refinedev/mui */ \"(rsc)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* harmony import */ var _refinedev_nextjs_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @refinedev/nextjs-router */ \"(rsc)/./node_modules/@refinedev/nextjs-router/dist/index.mjs\");\n/* harmony import */ var _providers_auth_provider_auth_provider_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @providers/auth-provider/auth-provider.client */ \"(rsc)/./src/providers/auth-provider/auth-provider.client.ts\");\n/* harmony import */ var _providers_data_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @providers/data-provider */ \"(rsc)/./src/providers/data-provider/index.ts\");\n/* harmony import */ var _contexts_color_mode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @contexts/color-mode */ \"(rsc)/./src/contexts/color-mode/index.tsx\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Student Admissions & Enrollment Management System\",\n    description: \"Comprehensive student lifecycle management system for educational institutions\",\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    const theme = cookieStore.get(\"theme\");\n    const defaultMode = theme?.value === \"dark\" ? \"dark\" : \"light\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_core__WEBPACK_IMPORTED_MODULE_7__.GitHubBanner, {}, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_kbar__WEBPACK_IMPORTED_MODULE_8__.RefineKbarProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_color_mode__WEBPACK_IMPORTED_MODULE_6__.ColorModeContextProvider, {\n                            defaultMode: defaultMode,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_mui__WEBPACK_IMPORTED_MODULE_9__.RefineSnackbarProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_devtools__WEBPACK_IMPORTED_MODULE_3__.DevtoolsProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_core__WEBPACK_IMPORTED_MODULE_7__.Refine, {\n                                        routerProvider: _refinedev_nextjs_router__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                        authProvider: _providers_auth_provider_auth_provider_client__WEBPACK_IMPORTED_MODULE_4__.authProviderClient,\n                                        dataProvider: _providers_data_provider__WEBPACK_IMPORTED_MODULE_5__.dataProvider,\n                                        notificationProvider: _refinedev_mui__WEBPACK_IMPORTED_MODULE_9__.useNotificationProvider,\n                                        resources: [\n                                            {\n                                                name: \"inquiries\",\n                                                list: \"/inquiries\",\n                                                create: \"/inquiries/create\",\n                                                edit: \"/inquiries/edit/:id\",\n                                                show: \"/inquiries/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Inquiries\",\n                                                    icon: \"contact_support\"\n                                                }\n                                            },\n                                            {\n                                                name: \"applications\",\n                                                list: \"/applications\",\n                                                create: \"/apply\",\n                                                edit: \"/applications/edit/:id\",\n                                                show: \"/applications/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Applications\",\n                                                    icon: \"assignment\"\n                                                }\n                                            },\n                                            {\n                                                name: \"interviews\",\n                                                list: \"/interviews\",\n                                                create: \"/interviews/create\",\n                                                edit: \"/interviews/edit/:id\",\n                                                show: \"/interviews/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Interviews\",\n                                                    icon: \"event\"\n                                                }\n                                            },\n                                            {\n                                                name: \"enrollments\",\n                                                list: \"/enrollments\",\n                                                create: \"/enrollments/create\",\n                                                edit: \"/enrollments/edit/:id\",\n                                                show: \"/enrollments/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Enrollments\",\n                                                    icon: \"school\"\n                                                }\n                                            },\n                                            {\n                                                name: \"capacity\",\n                                                list: \"/capacity\",\n                                                meta: {\n                                                    label: \"Capacity Management\",\n                                                    icon: \"group\"\n                                                }\n                                            },\n                                            {\n                                                name: \"communications\",\n                                                list: \"/communications\",\n                                                create: \"/communications/create\",\n                                                edit: \"/communications/edit/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Communications\",\n                                                    icon: \"email\"\n                                                }\n                                            },\n                                            {\n                                                name: \"reports\",\n                                                list: \"/reports\",\n                                                meta: {\n                                                    label: \"Reports & Analytics\",\n                                                    icon: \"analytics\"\n                                                }\n                                            }\n                                        ],\n                                        options: {\n                                            syncWithLocation: true,\n                                            warnWhenUnsavedChanges: true,\n                                            useNewQueryKeys: true,\n                                            projectId: \"7BvQym-ncceep-txvsN1\"\n                                        },\n                                        children: [\n                                            children,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_kbar__WEBPACK_IMPORTED_MODULE_8__.RefineKbar, {}, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 1\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 41,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 40,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\app\not-found.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/contexts/color-mode/index.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/color-mode/index.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ColorModeContext: () => (/* binding */ e0),
/* harmony export */   ColorModeContextProvider: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\contexts\color-mode\index.tsx#ColorModeContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\contexts\color-mode\index.tsx#ColorModeContextProvider`);


/***/ }),

/***/ "(rsc)/./src/providers/auth-provider/auth-provider.client.ts":
/*!*************************************************************!*\
  !*** ./src/providers/auth-provider/auth-provider.client.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   authProviderClient: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\providers\auth-provider\auth-provider.client.ts#authProviderClient`);


/***/ }),

/***/ "(rsc)/./src/providers/data-provider/index.ts":
/*!**********************************************!*\
  !*** ./src/providers/data-provider/index.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   dataProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\providers\data-provider\index.ts#dataProvider`);


/***/ }),

/***/ "(rsc)/./src/providers/devtools/index.tsx":
/*!******************************************!*\
  !*** ./src/providers/devtools/index.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DevtoolsProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\providers\devtools\index.tsx#DevtoolsProvider`);


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__ ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"48x48\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"icon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"?7391c51acd569043\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2ljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vc3JjL2FwcC9pY29uLmljbz8wMzY4Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjQ4eDQ4XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiaWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiPzczOTFjNTFhY2Q1NjkwNDNcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@mui","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/@refinedev","vendor-chunks/ws","vendor-chunks/micromark","vendor-chunks/lodash-es","vendor-chunks/@tanstack","vendor-chunks/react-hook-form","vendor-chunks/@emotion","vendor-chunks/whatwg-url","vendor-chunks/kbar","vendor-chunks/@popperjs","vendor-chunks/papaparse","vendor-chunks/fuse.js","vendor-chunks/notistack","vendor-chunks/prop-types","vendor-chunks/qs","vendor-chunks/property-information","vendor-chunks/react-transition-group","vendor-chunks/character-entities","vendor-chunks/react-markdown","vendor-chunks/stylis","vendor-chunks/mdast-util-from-markdown","vendor-chunks/object-inspect","vendor-chunks/mdast-util-to-hast","vendor-chunks/@radix-ui","vendor-chunks/ramda","vendor-chunks/get-intrinsic","vendor-chunks/react-virtual","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/pluralize","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/cookie","vendor-chunks/unified","vendor-chunks/fast-equals","vendor-chunks/webidl-conversions","vendor-chunks/hoist-non-react-statics","vendor-chunks/error-stack-parser","vendor-chunks/mdast-util-to-markdown","vendor-chunks/dayjs","vendor-chunks/markdown-table","vendor-chunks/vfile","vendor-chunks/inline-style-parser","vendor-chunks/react-is","vendor-chunks/stackframe","vendor-chunks/mdast-util-gfm-table","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/use-sync-external-store","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/js-cookie","vendor-chunks/side-channel-list","vendor-chunks/extend","vendor-chunks/trough","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/mdurl","vendor-chunks/function-bind","vendor-chunks/unist-util-visit-parents","vendor-chunks/object-assign","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/side-channel-map","vendor-chunks/vfile-message","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/@babel","vendor-chunks/mdast-util-gfm","vendor-chunks/isows","vendor-chunks/unist-util-is","vendor-chunks/repeat-string","vendor-chunks/side-channel","vendor-chunks/comma-separated-tokens","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/mdast-util-definitions","vendor-chunks/remark-gfm","vendor-chunks/remark-rehype","vendor-chunks/dunder-proto","vendor-chunks/style-to-object","vendor-chunks/@aliemir","vendor-chunks/unist-util-stringify-position","vendor-chunks/math-intrinsics","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/call-bound","vendor-chunks/unist-util-visit","vendor-chunks/mdast-util-to-string","vendor-chunks/remark-parse","vendor-chunks/unist-util-position","vendor-chunks/es-errors","vendor-chunks/micromark-extension-gfm","vendor-chunks/tiny-invariant","vendor-chunks/escape-string-regexp","vendor-chunks/unist-builder","vendor-chunks/clsx","vendor-chunks/xtend","vendor-chunks/ccount","vendor-chunks/space-separated-tokens","vendor-chunks/warn-once","vendor-chunks/unist-util-generated","vendor-chunks/gopd","vendor-chunks/is-buffer","vendor-chunks/es-define-property","vendor-chunks/parse-entities","vendor-chunks/is-plain-obj","vendor-chunks/hasown","vendor-chunks/bail","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcommunications%2Fpage&page=%2Fcommunications%2Fpage&appPaths=%2Fcommunications%2Fpage&pagePath=private-next-app-dir%2Fcommunications%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();