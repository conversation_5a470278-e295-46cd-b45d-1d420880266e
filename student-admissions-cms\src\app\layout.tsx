import { Metadata } from "next";
import { cookies } from "next/headers";import React, { Suspense } from 'react'
import { Refine, GitHubBanner, } from '@refinedev/core';
import { DevtoolsProvider } from '@providers/devtools'
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";
    import { useNotificationProvider
,RefineSnackbarProvider} from '@refinedev/mui';
import routerProvider from "@refinedev/nextjs-router";


import { authProviderClient } from "@providers/auth-provider/auth-provider.client";
import { dataProvider } from "@providers/data-provider";
import { ColorModeContextProvider } from "@contexts/color-mode";
import { Header } from "@components/header";




export const metadata: Metadata = {
    title: "Refine",
    description: "Generated by create refine app",
    icons: {
        icon: "/favicon.ico",
    },
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {

    
    
    const cookieStore = cookies();
const theme = cookieStore.get("theme");
const defaultMode = theme?.value === "dark" ? "dark" : "light";

    return (
        <html lang="en">
            <body>
            <Suspense>
                <GitHubBanner />
                <RefineKbarProvider>
                <ColorModeContextProvider defaultMode={defaultMode}>
<RefineSnackbarProvider>
                <DevtoolsProvider>
                    <Refine 
                        routerProvider={routerProvider}
                        authProvider={authProviderClient}
dataProvider={dataProvider}
notificationProvider={useNotificationProvider}
                        resources={[
                            {
                                name: "blog_posts",
                                list: "/blog-posts",
                                create: "/blog-posts/create",
                                edit: "/blog-posts/edit/:id",
                                show: "/blog-posts/show/:id",
                                meta: {
                                    canDelete: true,
                                },
                            },
                            {
                                name: "categories",
                                list: "/categories",
                                create: "/categories/create",
                                edit: "/categories/edit/:id",
                                show: "/categories/show/:id",
                                meta: {
                                    canDelete: true,
                                },
                            }
                        ]}
                        options={{
                            syncWithLocation: true,
                            warnWhenUnsavedChanges: true,
                            useNewQueryKeys: true,
                                projectId: "7BvQym-ncceep-txvsN1",
                             
                        }}
                    >
                        {children}
                        <RefineKbar />
                    </Refine>
                </DevtoolsProvider>
                </RefineSnackbarProvider>
</ColorModeContextProvider>
                </RefineKbarProvider>
            </Suspense>
            </body>
        </html>
    );
}
