'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  ContactSupport as ContactSupportIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { mockData } from '@/components/DummyDataProvider';

const SimpleInquiriesPage: React.FC = () => {
  const inquiries = mockData.inquiries;

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      new: 'info',
      contacted: 'primary',
      follow_up: 'warning',
      converted: 'success',
      closed: 'default',
    };
    return colors[status] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Inquiries Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage student inquiries and track leads through the admission process.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => alert('Create new inquiry functionality would be implemented here')}
        >
          New Inquiry
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ContactSupportIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {inquiries.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Inquiries
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ContactSupportIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {inquiries.filter(i => i.status === 'new').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    New Inquiries
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ContactSupportIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {inquiries.filter(i => i.status === 'converted').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Converted
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ContactSupportIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {inquiries.filter(i => i.status === 'follow_up').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Follow Up
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Inquiries Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            All Inquiries
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Inquiry #</TableCell>
                  <TableCell>Full Name</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Program</TableCell>
                  <TableCell>Source</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {inquiries.map((inquiry) => (
                  <TableRow key={inquiry.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="primary">
                        {inquiry.inquiry_number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {inquiry.full_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <EmailIcon fontSize="small" color="action" />
                          <Typography variant="body2">{inquiry.email}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <PhoneIcon fontSize="small" color="action" />
                          <Typography variant="body2">{inquiry.phone}</Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {inquiry.academic_programs?.name || 'Not specified'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip label={inquiry.inquiry_source} size="small" variant="outlined" />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={inquiry.status.replace('_', ' ').toUpperCase()}
                        color={getStatusColor(inquiry.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {inquiry.inquiry_date}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`View inquiry ${inquiry.inquiry_number}`)}
                        >
                          View
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`Edit inquiry ${inquiry.inquiry_number}`)}
                        >
                          Edit
                        </Button>
                        {!inquiry.converted_to_application && (
                          <Button
                            size="small"
                            variant="contained"
                            color="success"
                            onClick={() => alert(`Convert inquiry ${inquiry.inquiry_number} to application`)}
                          >
                            Convert
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes. 
          In a real implementation, this would connect to your database and allow full CRUD operations.
        </Typography>
      </Box>
    </Box>
  );
};

export default SimpleInquiriesPage;
