'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
} from '@mui/material';
import {
  Groups as GroupsIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Edit as EditIcon,
  List as ListIcon,
  School as SchoolIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`capacity-tabpanel-${index}`}
      aria-labelledby={`capacity-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const GroupCapacityPage: React.FC = () => {
  const [tabValue, setTabValue] = React.useState(0);

  // Dummy data for group capacity management
  const cohorts = [
    {
      id: '1',
      name: 'Fall 2024 Engineering Cohort',
      program: 'Computer Science',
      start_date: '2024-09-01',
      capacity: 120,
      enrolled: 98,
      waitlisted: 15,
      status: 'active',
      coordinator: 'Dr. Sarah Johnson',
    },
    {
      id: '2',
      name: 'Spring 2024 Business Cohort',
      program: 'Business Administration',
      start_date: '2024-01-15',
      capacity: 80,
      enrolled: 75,
      waitlisted: 8,
      status: 'active',
      coordinator: 'Prof. Michael Chen',
    },
    {
      id: '3',
      name: 'Summer 2024 Design Intensive',
      program: 'Graphic Design',
      start_date: '2024-06-01',
      capacity: 40,
      enrolled: 40,
      waitlisted: 12,
      status: 'full',
      coordinator: 'Ms. Emily Rodriguez',
    },
    {
      id: '4',
      name: 'Fall 2024 Medical Prep',
      program: 'Pre-Medicine',
      start_date: '2024-09-01',
      capacity: 60,
      enrolled: 45,
      waitlisted: 3,
      status: 'active',
      coordinator: 'Dr. James Wilson',
    },
  ];

  const groupClasses = [
    {
      id: '1',
      cohort_id: '1',
      class_name: 'CS101 - Programming Fundamentals',
      instructor: 'Prof. Alice Smith',
      capacity: 30,
      enrolled: 28,
      schedule: 'Mon/Wed/Fri 9:00-10:30',
      room: 'Lab A-101',
    },
    {
      id: '2',
      cohort_id: '1',
      class_name: 'CS102 - Data Structures',
      instructor: 'Dr. Bob Johnson',
      capacity: 30,
      enrolled: 25,
      schedule: 'Tue/Thu 11:00-12:30',
      room: 'Lab A-102',
    },
    {
      id: '3',
      cohort_id: '2',
      class_name: 'BUS201 - Business Strategy',
      instructor: 'Prof. Carol Davis',
      capacity: 40,
      enrolled: 38,
      schedule: 'Mon/Wed 2:00-3:30',
      room: 'Room B-201',
    },
    {
      id: '4',
      cohort_id: '3',
      class_name: 'DES301 - Advanced Typography',
      instructor: 'Ms. Diana Lee',
      capacity: 20,
      enrolled: 20,
      schedule: 'Tue/Thu 10:00-12:00',
      room: 'Studio C-301',
    },
  ];

  const getCapacityStatus = (current: number, capacity: number) => {
    const percentage = (current / capacity) * 100;
    if (percentage >= 100) return { status: 'full', color: 'error' as const };
    if (percentage >= 90) return { status: 'nearly_full', color: 'warning' as const };
    if (percentage >= 70) return { status: 'filling', color: 'info' as const };
    return { status: 'available', color: 'success' as const };
  };

  const getVacancies = (current: number, capacity: number) => {
    return Math.max(0, capacity - current);
  };

  // Calculate summary statistics
  const totalCohorts = cohorts.length;
  const totalCapacity = cohorts.reduce((sum, cohort) => sum + cohort.capacity, 0);
  const totalEnrolled = cohorts.reduce((sum, cohort) => sum + cohort.enrolled, 0);
  const totalWaitlisted = cohorts.reduce((sum, cohort) => sum + cohort.waitlisted, 0);
  const utilizationRate = ((totalEnrolled / totalCapacity) * 100).toFixed(1);

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Group Capacity Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage cohort capacities, group enrollments, and batch admissions across programs.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<GroupsIcon />}
          onClick={() => alert('Create new cohort functionality would be implemented here')}
        >
          New Cohort
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <GroupsIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {totalCohorts}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Cohorts
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PeopleIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {totalEnrolled}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Enrolled
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ScheduleIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {totalWaitlisted}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Waitlisted
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingUpIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {utilizationRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Utilization Rate
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Cohort Overview" />
          <Tab label="Group Classes" />
          <Tab label="Batch Operations" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Typography variant="h6" gutterBottom>
          Cohort Capacity Overview
        </Typography>
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Cohort Name</TableCell>
                <TableCell>Program</TableCell>
                <TableCell>Start Date</TableCell>
                <TableCell>Capacity</TableCell>
                <TableCell>Enrolled</TableCell>
                <TableCell>Waitlisted</TableCell>
                <TableCell>Utilization</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Coordinator</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {cohorts.map((cohort) => {
                const vacancies = getVacancies(cohort.enrolled, cohort.capacity);
                const percentage = (cohort.enrolled / cohort.capacity) * 100;
                const { status, color } = getCapacityStatus(cohort.enrolled, cohort.capacity);
                
                const statusLabels: Record<string, string> = {
                  full: 'Full',
                  nearly_full: 'Nearly Full',
                  filling: 'Filling',
                  available: 'Available',
                };

                return (
                  <TableRow key={cohort.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {cohort.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {cohort.program}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(cohort.start_date).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {cohort.capacity}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="primary">
                        {cohort.enrolled}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="warning.main">
                        {cohort.waitlisted}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ width: '100%' }}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(percentage, 100)}
                          color={color}
                          sx={{ height: 8, borderRadius: 4, mb: 0.5 }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {percentage.toFixed(1)}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={statusLabels[status]}
                        color={color}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {cohort.coordinator}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<EditIcon />}
                          onClick={() => alert(`Edit cohort: ${cohort.name}`)}
                        >
                          Edit
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<ListIcon />}
                          onClick={() => alert(`View details: ${cohort.name}`)}
                        >
                          Details
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Typography variant="h6" gutterBottom>
          Group Classes by Cohort
        </Typography>
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Class Name</TableCell>
                <TableCell>Instructor</TableCell>
                <TableCell>Schedule</TableCell>
                <TableCell>Room</TableCell>
                <TableCell>Capacity</TableCell>
                <TableCell>Enrolled</TableCell>
                <TableCell>Utilization</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {groupClasses.map((groupClass) => {
                const percentage = (groupClass.enrolled / groupClass.capacity) * 100;
                const { color } = getCapacityStatus(groupClass.enrolled, groupClass.capacity);
                const cohort = cohorts.find(c => c.id === groupClass.cohort_id);

                return (
                  <TableRow key={groupClass.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {groupClass.class_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {cohort?.name}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {groupClass.instructor}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {groupClass.schedule}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {groupClass.room}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {groupClass.capacity}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="primary">
                        {groupClass.enrolled}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ width: '100%' }}>
                        <LinearProgress
                          variant="determinate"
                          value={Math.min(percentage, 100)}
                          color={color}
                          sx={{ height: 8, borderRadius: 4, mb: 0.5 }}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {percentage.toFixed(1)}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<EditIcon />}
                          onClick={() => alert(`Edit class: ${groupClass.class_name}`)}
                        >
                          Edit
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<PeopleIcon />}
                          onClick={() => alert(`Manage students: ${groupClass.class_name}`)}
                        >
                          Students
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" gutterBottom>
          Batch Operations
        </Typography>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <AssignmentIcon color="primary" />
                  <Typography variant="h6">
                    Bulk Enrollment
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Enroll multiple students into cohorts simultaneously
                </Typography>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => alert('Bulk enrollment functionality would be implemented here')}
                >
                  Start Bulk Enrollment
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <ScheduleIcon color="warning" />
                  <Typography variant="h6">
                    Waitlist Management
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Process waitlisted students across all cohorts
                </Typography>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => alert('Waitlist management functionality would be implemented here')}
                >
                  Process Waitlists
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <SchoolIcon color="success" />
                  <Typography variant="h6">
                    Cohort Transfer
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Transfer students between cohorts and programs
                </Typography>
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={() => alert('Cohort transfer functionality would be implemented here')}
                >
                  Manage Transfers
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Batch Operation Features
          </Typography>
          <Typography variant="body2">
            The batch operations system includes:
          </Typography>
          <ul>
            <li>Bulk student enrollment with CSV import</li>
            <li>Automated waitlist processing and notifications</li>
            <li>Cross-cohort student transfers</li>
            <li>Capacity rebalancing across programs</li>
            <li>Batch communication to cohort groups</li>
            <li>Academic calendar integration</li>
          </ul>
        </Alert>
      </TabPanel>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes.
          In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Real-time cohort capacity monitoring</li>
          <li>Automated enrollment workflows</li>
          <li>Integration with academic calendar systems</li>
          <li>Advanced analytics and forecasting</li>
          <li>Student communication automation</li>
          <li>Resource allocation optimization</li>
        </ul>
      </Box>
    </Box>
  );
};

export default GroupCapacityPage;
