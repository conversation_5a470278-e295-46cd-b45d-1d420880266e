'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Email as EmailIcon,
  Sms as SmsIcon,
  WhatsApp as WhatsAppIcon,
  Add as AddIcon,
  Send as SendIcon,
  Article as TemplateIcon,
  Notifications as NotificationsIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Preview as PreviewIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const CommunicationsPage: React.FC = () => {
  const [tabValue, setTabValue] = React.useState(0);

  // Enhanced dummy data for communications
  const templates = [
    {
      id: '1',
      name: 'Application Received',
      type: 'email',
      subject: 'Your Application Has Been Received - {{application_number}}',
      content: 'Dear {{first_name}}, Thank you for submitting your application for {{program_name}}.',
      variables: ['first_name', 'application_number', 'program_name'],
      is_active: true,
      created_at: '2024-01-15T10:00:00Z',
      usage_count: 45
    },
    {
      id: '2',
      name: 'Interview Invitation',
      type: 'email',
      subject: 'Interview Invitation - {{program_name}}',
      content: 'Dear {{first_name}}, You are invited for an interview on {{interview_date}}.',
      variables: ['first_name', 'program_name', 'interview_date', 'interview_time'],
      is_active: true,
      created_at: '2024-01-10T14:30:00Z',
      usage_count: 23
    },
    {
      id: '3',
      name: 'Admission Offer',
      type: 'email',
      subject: 'Congratulations! Admission Offer - {{program_name}}',
      content: 'Dear {{first_name}}, Congratulations! You have been offered admission to {{program_name}}.',
      variables: ['first_name', 'program_name', 'offer_deadline'],
      is_active: true,
      created_at: '2024-01-05T09:15:00Z',
      usage_count: 67
    },
    {
      id: '4',
      name: 'Document Reminder',
      type: 'sms',
      subject: '',
      content: 'Hi {{first_name}}, Please submit your pending documents for application {{application_number}}.',
      variables: ['first_name', 'application_number'],
      is_active: true,
      created_at: '2024-01-20T16:45:00Z',
      usage_count: 12
    },
    {
      id: '5',
      name: 'Fee Payment Reminder',
      type: 'whatsapp',
      subject: '',
      content: 'Hello {{first_name}}, Your fee payment of ${{amount}} is due by {{due_date}}.',
      variables: ['first_name', 'amount', 'due_date'],
      is_active: true,
      created_at: '2024-01-18T11:20:00Z',
      usage_count: 34
    }
  ];

  const notifications = [
    {
      id: '1',
      template_name: 'Application Received',
      recipient: '<EMAIL>',
      type: 'email',
      status: 'delivered',
      sent_at: '2024-01-22T10:30:00Z',
      delivered_at: '2024-01-22T10:31:15Z'
    },
    {
      id: '2',
      template_name: 'Interview Invitation',
      recipient: '<EMAIL>',
      type: 'email',
      status: 'delivered',
      sent_at: '2024-01-22T09:15:00Z',
      delivered_at: '2024-01-22T09:16:22Z'
    },
    {
      id: '3',
      template_name: 'Document Reminder',
      recipient: '******-0123',
      type: 'sms',
      status: 'failed',
      sent_at: '2024-01-22T08:45:00Z',
      error_message: 'Invalid phone number'
    },
    {
      id: '4',
      template_name: 'Admission Offer',
      recipient: '<EMAIL>',
      type: 'email',
      status: 'pending',
      sent_at: '2024-01-22T11:00:00Z'
    }
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <EmailIcon />;
      case 'sms':
        return <SmsIcon />;
      case 'whatsapp':
        return <WhatsAppIcon />;
      default:
        return <EmailIcon />;
    }
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      email: 'primary',
      sms: 'success',
      whatsapp: 'info',
      in_app: 'secondary',
    };
    return colors[type] || 'default';
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      delivered: 'success',
      pending: 'warning',
      failed: 'error',
      sent: 'info',
    };
    return colors[status] || 'default';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckIcon />;
      case 'failed':
        return <ErrorIcon />;
      case 'pending':
        return <ScheduleIcon />;
      default:
        return <SendIcon />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Communication Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage communication templates and send notifications to applicants and students.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => alert('Create new template functionality would be implemented here')}
        >
          New Template
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TemplateIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {templates.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Templates
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SendIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {notifications.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Sent Today
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CheckIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {notifications.filter(n => n.status === 'delivered').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Delivered
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ErrorIcon color="error" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="error.main">
                    {notifications.filter(n => n.status === 'failed').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Failed
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="Templates" />
          <Tab label="Send Notifications" />
          <Tab label="Notification History" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6">Communication Templates</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => alert('Create new template functionality would be implemented here')}
          >
            New Template
          </Button>
        </Box>

        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Template Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Subject</TableCell>
                <TableCell>Variables</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Usage</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {templates.map((template) => (
                <TableRow key={template.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {template.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getTypeIcon(template.type)}
                      label={template.type.toUpperCase()}
                      color={getTypeColor(template.type)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap>
                      {template.type === 'email' ? template.subject : 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {template.variables?.length || 0} variables
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={template.is_active ? 'Active' : 'Inactive'}
                      color={template.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="primary">
                      {template.usage_count} times
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<PreviewIcon />}
                        onClick={() => alert(`Preview template: ${template.name}`)}
                      >
                        Preview
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => alert(`Edit template: ${template.name}`)}
                      >
                        Edit
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Send Bulk Notifications
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Send notifications to multiple recipients at once
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<SendIcon />}
                  fullWidth
                  onClick={() => alert('Bulk notification functionality would be implemented here')}
                >
                  Send Bulk Notifications
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Send
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Send individual notifications using templates
                </Typography>
                <Button
                  variant="outlined"
                  startIcon={<EmailIcon />}
                  fullWidth
                  onClick={() => alert('Quick send functionality would be implemented here')}
                >
                  Quick Send
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Bulk Notification Features
          </Typography>
          <Typography variant="body2">
            The bulk notification system would include:
          </Typography>
          <ul>
            <li>Recipient group selection (all applicants, specific status, program, etc.)</li>
            <li>Template selection with variable substitution</li>
            <li>Preview before sending</li>
            <li>Scheduled sending</li>
            <li>Delivery tracking and analytics</li>
          </ul>
        </Alert>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" gutterBottom>
          Recent Notifications
        </Typography>

        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Template</TableCell>
                <TableCell>Recipient</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Sent At</TableCell>
                <TableCell>Delivered At</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {notifications.map((notification) => (
                <TableRow key={notification.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {notification.template_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {notification.recipient}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getTypeIcon(notification.type)}
                      label={notification.type.toUpperCase()}
                      color={getTypeColor(notification.type)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getStatusIcon(notification.status)}
                      label={notification.status.toUpperCase()}
                      color={getStatusColor(notification.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(notification.sent_at).toLocaleString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {notification.delivered_at
                        ? new Date(notification.delivered_at).toLocaleString()
                        : notification.error_message || 'N/A'
                      }
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes.
          In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Rich text editor for template creation</li>
          <li>Variable insertion and preview functionality</li>
          <li>Integration with email/SMS/WhatsApp providers</li>
          <li>Automated trigger-based notifications</li>
          <li>A/B testing for templates</li>
          <li>Comprehensive delivery and engagement analytics</li>
        </ul>
      </Box>
    </Box>
  );
};

export default CommunicationsPage;
