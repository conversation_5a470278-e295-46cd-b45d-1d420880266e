"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/communications/page",{

/***/ "(app-pages-browser)/./node_modules/@mui/icons-material/esm/Article.js":
/*!*********************************************************!*\
  !*** ./node_modules/@mui/icons-material/esm/Article.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _utils_createSvgIcon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/createSvgIcon.js */ \"(app-pages-browser)/./node_modules/@mui/material/utils/createSvgIcon.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/* harmony default export */ __webpack_exports__[\"default\"] = ((0,_utils_createSvgIcon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n    d: \"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-5 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z\"\n}), \"Article\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbXVpL2ljb25zLW1hdGVyaWFsL2VzbS9BcnRpY2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs2REFFcUQ7QUFDTDtBQUNoRCwrREFBZUEsbUVBQWFBLENBQUMsV0FBVyxHQUFFRSxzREFBSUEsQ0FBQyxRQUFRO0lBQ3JEQyxHQUFHO0FBQ0wsSUFBSSxZQUFXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AbXVpL2ljb25zLW1hdGVyaWFsL2VzbS9BcnRpY2xlLmpzP2RiZTEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBjcmVhdGVTdmdJY29uIGZyb20gXCIuL3V0aWxzL2NyZWF0ZVN2Z0ljb24uanNcIjtcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgZGVmYXVsdCBjcmVhdGVTdmdJY29uKC8qI19fUFVSRV9fKi9fanN4KFwicGF0aFwiLCB7XG4gIGQ6IFwiTTE5IDNINWMtMS4xIDAtMiAuOS0yIDJ2MTRjMCAxLjEuOSAyIDIgMmgxNGMxLjEgMCAyLS45IDItMlY1YzAtMS4xLS45LTItMi0ybS01IDE0SDd2LTJoN3ptMy00SDd2LTJoMTB6bTAtNEg3VjdoMTB6XCJcbn0pLCAnQXJ0aWNsZScpOyJdLCJuYW1lcyI6WyJjcmVhdGVTdmdJY29uIiwianN4IiwiX2pzeCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@mui/icons-material/esm/Article.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/communications/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/communications/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tabs/Tabs.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tab/Tab.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,Grid,Paper,Tab,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Tabs,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Email.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Sms.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/WhatsApp.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Article.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Preview.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Article,CheckCircle,Edit,Email,Error,Preview,Schedule,Send,Sms,WhatsApp!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Edit.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TabPanel(props) {\n    const { children, value, index, ...other } = props;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        role: \"tabpanel\",\n        hidden: value !== index,\n        id: \"simple-tabpanel-\".concat(index),\n        \"aria-labelledby\": \"simple-tab-\".concat(index),\n        ...other,\n        children: value === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n            lineNumber: 55,\n            columnNumber: 27\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_c = TabPanel;\nconst CommunicationsPage = ()=>{\n    _s();\n    const [tabValue, setTabValue] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    // Enhanced dummy data for communications\n    const templates = [\n        {\n            id: \"1\",\n            name: \"Application Received\",\n            type: \"email\",\n            subject: \"Your Application Has Been Received - {{application_number}}\",\n            content: \"Dear {{first_name}}, Thank you for submitting your application for {{program_name}}.\",\n            variables: [\n                \"first_name\",\n                \"application_number\",\n                \"program_name\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-15T10:00:00Z\",\n            usage_count: 45\n        },\n        {\n            id: \"2\",\n            name: \"Interview Invitation\",\n            type: \"email\",\n            subject: \"Interview Invitation - {{program_name}}\",\n            content: \"Dear {{first_name}}, You are invited for an interview on {{interview_date}}.\",\n            variables: [\n                \"first_name\",\n                \"program_name\",\n                \"interview_date\",\n                \"interview_time\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-10T14:30:00Z\",\n            usage_count: 23\n        },\n        {\n            id: \"3\",\n            name: \"Admission Offer\",\n            type: \"email\",\n            subject: \"Congratulations! Admission Offer - {{program_name}}\",\n            content: \"Dear {{first_name}}, Congratulations! You have been offered admission to {{program_name}}.\",\n            variables: [\n                \"first_name\",\n                \"program_name\",\n                \"offer_deadline\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-05T09:15:00Z\",\n            usage_count: 67\n        },\n        {\n            id: \"4\",\n            name: \"Document Reminder\",\n            type: \"sms\",\n            subject: \"\",\n            content: \"Hi {{first_name}}, Please submit your pending documents for application {{application_number}}.\",\n            variables: [\n                \"first_name\",\n                \"application_number\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-20T16:45:00Z\",\n            usage_count: 12\n        },\n        {\n            id: \"5\",\n            name: \"Fee Payment Reminder\",\n            type: \"whatsapp\",\n            subject: \"\",\n            content: \"Hello {{first_name}}, Your fee payment of ${{amount}} is due by {{due_date}}.\",\n            variables: [\n                \"first_name\",\n                \"amount\",\n                \"due_date\"\n            ],\n            is_active: true,\n            created_at: \"2024-01-18T11:20:00Z\",\n            usage_count: 34\n        }\n    ];\n    const notifications = [\n        {\n            id: \"1\",\n            template_name: \"Application Received\",\n            recipient: \"<EMAIL>\",\n            type: \"email\",\n            status: \"delivered\",\n            sent_at: \"2024-01-22T10:30:00Z\",\n            delivered_at: \"2024-01-22T10:31:15Z\"\n        },\n        {\n            id: \"2\",\n            template_name: \"Interview Invitation\",\n            recipient: \"<EMAIL>\",\n            type: \"email\",\n            status: \"delivered\",\n            sent_at: \"2024-01-22T09:15:00Z\",\n            delivered_at: \"2024-01-22T09:16:22Z\"\n        },\n        {\n            id: \"3\",\n            template_name: \"Document Reminder\",\n            recipient: \"******-0123\",\n            type: \"sms\",\n            status: \"failed\",\n            sent_at: \"2024-01-22T08:45:00Z\",\n            error_message: \"Invalid phone number\"\n        },\n        {\n            id: \"4\",\n            template_name: \"Admission Offer\",\n            recipient: \"<EMAIL>\",\n            type: \"email\",\n            status: \"pending\",\n            sent_at: \"2024-01-22T11:00:00Z\"\n        }\n    ];\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"email\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 16\n                }, undefined);\n            case \"sms\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 16\n                }, undefined);\n            case \"whatsapp\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTypeColor = (type)=>{\n        const colors = {\n            email: \"primary\",\n            sms: \"success\",\n            whatsapp: \"info\",\n            in_app: \"secondary\"\n        };\n        return colors[type] || \"default\";\n    };\n    const getStatusColor = (status)=>{\n        const colors = {\n            delivered: \"success\",\n            pending: \"warning\",\n            failed: \"error\",\n            sent: \"info\"\n        };\n        return colors[status] || \"default\";\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"delivered\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 16\n                }, undefined);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 16\n                }, undefined);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    mb: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h4\",\n                                gutterBottom: true,\n                                children: \"Communication Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"body1\",\n                                color: \"text.secondary\",\n                                children: \"Manage communication templates and send notifications to applicants and students.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        variant: \"contained\",\n                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 22\n                        }, void 0),\n                        onClick: ()=>alert(\"Create new template functionality would be implemented here\"),\n                        children: \"New Template\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            color: \"primary\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"primary\",\n                                                    children: templates.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Active Templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            color: \"info\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"info.main\",\n                                                    children: notifications.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Sent Today\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            color: \"success\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"success.main\",\n                                                    children: notifications.filter((n)=>n.status === \"delivered\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Delivered\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            color: \"error\",\n                                            sx: {\n                                                fontSize: 40\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"h4\",\n                                                    color: \"error.main\",\n                                                    children: notifications.filter((n)=>n.status === \"failed\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"text.secondary\",\n                                                    children: \"Failed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    borderBottom: 1,\n                    borderColor: \"divider\",\n                    mb: 3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    value: tabValue,\n                    onChange: (_, newValue)=>setTabValue(newValue),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            label: \"Templates\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            label: \"Send Notifications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            label: \"Notification History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                value: tabValue,\n                index: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h6\",\n                                children: \"Communication Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: ()=>alert(\"Create new template functionality would be implemented here\"),\n                                children: \"New Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        component: _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        variant: \"outlined\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Template Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Subject\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Variables\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Usage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    children: templates.map((template)=>{\n                                        var _template_variables;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        fontWeight: \"bold\",\n                                                        children: template.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        icon: getTypeIcon(template.type),\n                                                        label: template.type.toUpperCase(),\n                                                        color: getTypeColor(template.type),\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        noWrap: true,\n                                                        children: template.type === \"email\" ? template.subject : \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        color: \"text.secondary\",\n                                                        children: [\n                                                            ((_template_variables = template.variables) === null || _template_variables === void 0 ? void 0 : _template_variables.length) || 0,\n                                                            \" variables\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        label: template.is_active ? \"Active\" : \"Inactive\",\n                                                        color: template.is_active ? \"success\" : \"default\",\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        color: \"primary\",\n                                                        children: [\n                                                            template.usage_count,\n                                                            \" times\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            gap: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                size: \"small\",\n                                                                variant: \"outlined\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                onClick: ()=>alert(\"Preview template: \".concat(template.name)),\n                                                                children: \"Preview\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                size: \"small\",\n                                                                variant: \"outlined\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                onClick: ()=>alert(\"Edit template: \".concat(template.name)),\n                                                                children: \"Edit\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, template.id, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                value: tabValue,\n                index: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Send Bulk Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                gutterBottom: true,\n                                                children: \"Send notifications to multiple recipients at once\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"contained\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 30\n                                                }, void 0),\n                                                fullWidth: true,\n                                                onClick: ()=>alert(\"Bulk notification functionality would be implemented here\"),\n                                                children: \"Send Bulk Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Quick Send\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"text.secondary\",\n                                                gutterBottom: true,\n                                                children: \"Send individual notifications using templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Article_CheckCircle_Edit_Email_Error_Preview_Schedule_Send_Sms_WhatsApp_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 30\n                                                }, void 0),\n                                                fullWidth: true,\n                                                onClick: ()=>alert(\"Quick send functionality would be implemented here\"),\n                                                children: \"Quick Send\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        severity: \"info\",\n                        sx: {\n                            mt: 3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h6\",\n                                gutterBottom: true,\n                                children: \"Bulk Notification Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"body2\",\n                                children: \"The bulk notification system would include:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Recipient group selection (all applicants, specific status, program, etc.)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Template selection with variable substitution\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Preview before sending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Scheduled sending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Delivery tracking and analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                value: tabValue,\n                index: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Recent Notifications\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        component: _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                        variant: \"outlined\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Recipient\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Sent At\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                children: \"Delivered At\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        fontWeight: \"bold\",\n                                                        children: notification.template_name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: notification.recipient\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        icon: getTypeIcon(notification.type),\n                                                        label: notification.type.toUpperCase(),\n                                                        color: getTypeColor(notification.type),\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        icon: getStatusIcon(notification.status),\n                                                        label: notification.status.toUpperCase(),\n                                                        color: getStatusColor(notification.status),\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: new Date(notification.sent_at).toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: notification.delivered_at ? new Date(notification.delivered_at).toLocaleString() : notification.error_message || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, notification.id, true, {\n                                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    mt: 3,\n                    p: 2,\n                    bgcolor: \"info.light\",\n                    borderRadius: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_Grid_Paper_Tab_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Tabs_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Demo Mode:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, undefined),\n                            \" This page displays dummy data for demonstration purposes. In a real implementation, this would include:\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Rich text editor for template creation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Variable insertion and preview functionality\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Integration with email/SMS/WhatsApp providers\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Automated trigger-based notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"A/B testing for templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Comprehensive delivery and engagement analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n                lineNumber: 525,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\communications\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CommunicationsPage, \"8xDoMf2dCMwhPWqj+mT3H7/i8ZA=\");\n_c1 = CommunicationsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CommunicationsPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c1, \"CommunicationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/communications/page.tsx\n"));

/***/ })

});