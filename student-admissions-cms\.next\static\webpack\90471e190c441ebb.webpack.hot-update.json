{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@aliemir/dom-to-fiber-utils/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.js", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.js", "(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.js", "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.js", "(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.js", "(app-pages-browser)/./node_modules/@refinedev/devtools/dist/index.mjs", "(app-pages-browser)/./node_modules/@refinedev/kbar/dist/index.mjs", "(app-pages-browser)/./node_modules/@refinedev/supabase/dist/index.mjs", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/AuthClient.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/index.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/base64url.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/errors.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/locks.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/types.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "(app-pages-browser)/./node_modules/@supabase/functions-js/dist/module/helper.js", "(app-pages-browser)/./node_modules/@supabase/functions-js/dist/module/types.js", "(app-pages-browser)/./node_modules/@supabase/node-fetch/browser.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/index.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/version.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/index.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/push.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/index.mjs", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/StorageClient.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/errors.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/fetch.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/helpers.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/cookie/index.js", "(app-pages-browser)/./node_modules/fast-equals/dist/fast-equals.js", "(app-pages-browser)/./node_modules/fuse.js/dist/fuse.esm.js", "(app-pages-browser)/./node_modules/isows/_esm/native.js", "(app-pages-browser)/./node_modules/isows/_esm/utils.js", "(app-pages-browser)/./node_modules/kbar/lib/InternalEvents.js", "(app-pages-browser)/./node_modules/kbar/lib/KBarAnimator.js", "(app-pages-browser)/./node_modules/kbar/lib/KBarContextProvider.js", "(app-pages-browser)/./node_modules/kbar/lib/KBarPortal.js", "(app-pages-browser)/./node_modules/kbar/lib/KBarPositioner.js", "(app-pages-browser)/./node_modules/kbar/lib/KBarResults.js", "(app-pages-browser)/./node_modules/kbar/lib/KBarSearch.js", "(app-pages-browser)/./node_modules/kbar/lib/action/ActionImpl.js", "(app-pages-browser)/./node_modules/kbar/lib/action/ActionInterface.js", "(app-pages-browser)/./node_modules/kbar/lib/action/Command.js", "(app-pages-browser)/./node_modules/kbar/lib/action/HistoryImpl.js", "(app-pages-browser)/./node_modules/kbar/lib/action/index.js", "(app-pages-browser)/./node_modules/kbar/lib/index.js", "(app-pages-browser)/./node_modules/kbar/lib/tinykeys.js", "(app-pages-browser)/./node_modules/kbar/lib/types.js", "(app-pages-browser)/./node_modules/kbar/lib/useKBar.js", "(app-pages-browser)/./node_modules/kbar/lib/useMatches.js", "(app-pages-browser)/./node_modules/kbar/lib/useRegisterActions.js", "(app-pages-browser)/./node_modules/kbar/lib/useStore.js", "(app-pages-browser)/./node_modules/kbar/lib/utils.js", "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ccore%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22GitHubBanner%22%2C%22Refine%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ckbar%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineKbarProvider%22%2C%22RefineKbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cmui%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineSnackbarProvider%22%2C%22useNotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cnextjs-router%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccontexts%5C%5Ccolor-mode%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22ColorModeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider%5C%5Cauth-provider.client.ts%22%2C%22ids%22%3A%5B%22authProviderClient%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdata-provider%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22dataProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdevtools%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22DevtoolsProvider%22%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js", "(app-pages-browser)/./node_modules/ramda/es/internal/_curry1.js", "(app-pages-browser)/./node_modules/ramda/es/internal/_curry2.js", "(app-pages-browser)/./node_modules/ramda/es/internal/_curry3.js", "(app-pages-browser)/./node_modules/ramda/es/internal/_has.js", "(app-pages-browser)/./node_modules/ramda/es/internal/_isObject.js", "(app-pages-browser)/./node_modules/ramda/es/internal/_isPlaceholder.js", "(app-pages-browser)/./node_modules/ramda/es/mergeDeepRight.js", "(app-pages-browser)/./node_modules/ramda/es/mergeDeepWithKey.js", "(app-pages-browser)/./node_modules/ramda/es/mergeWithKey.js", "(app-pages-browser)/./node_modules/react-virtual/dist/react-virtual.mjs", "(app-pages-browser)/./node_modules/tiny-invariant/dist/tiny-invariant.cjs.js", "(app-pages-browser)/./src/providers/auth-provider/auth-provider.client.ts", "(app-pages-browser)/./src/providers/data-provider/index.ts", "(app-pages-browser)/./src/providers/devtools/index.tsx", "(app-pages-browser)/./src/utils/supabase/client.ts", "(app-pages-browser)/./src/utils/supabase/constants.ts"]}