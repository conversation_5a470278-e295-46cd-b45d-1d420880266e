'use client';

import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  Group as GroupIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Edit as EditIcon,
  List as ListIcon,
} from '@mui/icons-material';
import { mockData } from '@/components/DummyDataProvider';

const SimpleCapacityPage: React.FC = () => {
  const classes = mockData.classes;

  const getCapacityStatus = (current: number, capacity: number) => {
    const percentage = (current / capacity) * 100;
    if (percentage >= 100) return { status: 'full', color: 'error' as const };
    if (percentage >= 90) return { status: 'nearly_full', color: 'warning' as const };
    if (percentage >= 70) return { status: 'filling', color: 'info' as const };
    return { status: 'available', color: 'success' as const };
  };

  const getVacancies = (current: number, capacity: number) => {
    return Math.max(0, capacity - current);
  };

  // Calculate summary statistics
  const totalCapacity = classes.reduce((sum, cls) => sum + cls.capacity, 0);
  const totalEnrolled = classes.reduce((sum, cls) => sum + cls.current_enrollment, 0);
  const totalVacancies = totalCapacity - totalEnrolled;
  const utilizationRate = ((totalEnrolled / totalCapacity) * 100).toFixed(1);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Capacity Management
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        Monitor class capacities, track enrollment, and manage waitlists.
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <GroupIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {totalCapacity}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Capacity
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CheckIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {totalEnrolled}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Current Enrollment
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingUpIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {totalVacancies}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Available Seats
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <WarningIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {utilizationRate}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Utilization Rate
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Capacity Overview */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Class Capacity Overview
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Program</TableCell>
                  <TableCell>Class</TableCell>
                  <TableCell>Level</TableCell>
                  <TableCell>Capacity</TableCell>
                  <TableCell>Enrolled</TableCell>
                  <TableCell>Vacancies</TableCell>
                  <TableCell>Utilization</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {classes.map((cls) => {
                  const vacancies = getVacancies(cls.current_enrollment, cls.capacity);
                  const percentage = (cls.current_enrollment / cls.capacity) * 100;
                  const { status, color } = getCapacityStatus(cls.current_enrollment, cls.capacity);
                  
                  const statusLabels = {
                    full: 'Full',
                    nearly_full: 'Nearly Full',
                    filling: 'Filling',
                    available: 'Available',
                  };

                  return (
                    <TableRow key={cls.id} hover>
                      <TableCell>
                        <Typography variant="body2">
                          {cls.academic_programs.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {cls.class_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip label={`Year ${cls.level}`} size="small" variant="outlined" />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {cls.capacity}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="primary">
                          {cls.current_enrollment}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography 
                          variant="body2" 
                          color={vacancies > 0 ? 'success.main' : 'error.main'}
                          fontWeight="bold"
                        >
                          {vacancies}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ width: '100%' }}>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(percentage, 100)}
                            color={color}
                            sx={{ height: 8, borderRadius: 4, mb: 0.5 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {percentage.toFixed(1)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={statusLabels[status]}
                          color={color}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<EditIcon />}
                            onClick={() => alert(`Edit capacity for ${cls.class_name}`)}
                          >
                            Edit
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<ListIcon />}
                            onClick={() => alert(`View waitlist for ${cls.class_name}`)}
                            disabled={vacancies > 0}
                          >
                            Waitlist
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Capacity Alerts */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Alert severity="warning">
            <Typography variant="h6" gutterBottom>
              Capacity Alerts
            </Typography>
            <Typography variant="body2">
              Classes nearing capacity:
            </Typography>
            <ul>
              {classes
                .filter(cls => {
                  const percentage = (cls.current_enrollment / cls.capacity) * 100;
                  return percentage >= 90;
                })
                .map(cls => (
                  <li key={cls.id}>
                    {cls.class_name} - {((cls.current_enrollment / cls.capacity) * 100).toFixed(1)}% full
                  </li>
                ))}
            </ul>
          </Alert>
        </Grid>
        <Grid item xs={12} md={6}>
          <Alert severity="info">
            <Typography variant="h6" gutterBottom>
              Waitlist Management
            </Typography>
            <Typography variant="body2">
              Automatically extend offers to waitlisted students when seats become available.
            </Typography>
            <Button
              variant="outlined"
              sx={{ mt: 2 }}
              onClick={() => alert('Waitlist management functionality would be implemented here')}
            >
              Manage Waitlists
            </Button>
          </Alert>
        </Grid>
      </Grid>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes. 
          In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Real-time capacity monitoring and alerts</li>
          <li>Automated waitlist management and offer extension</li>
          <li>Capacity planning and forecasting tools</li>
          <li>Integration with enrollment and application systems</li>
          <li>Historical capacity utilization reports</li>
          <li>Flexible capacity adjustment workflows</li>
        </ul>
      </Box>
    </Box>
  );
};

export default SimpleCapacityPage;
