'use client';

import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Event as EventIcon,
  Add as AddIcon,
  VideoCall as VideoCallIcon,
  LocationOn as LocationIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { mockData } from '@/components/DummyDataProvider';

const SimpleInterviewsPage: React.FC = () => {
  const interviews = mockData.interviews;

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      scheduled: 'primary',
      completed: 'success',
      cancelled: 'error',
      rescheduled: 'warning',
      no_show: 'error',
    };
    return colors[status] || 'default';
  };

  const getInterviewTypeColor = (type: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      personal: 'info',
      technical: 'primary',
      group: 'secondary',
      panel: 'warning',
    };
    return colors[type] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Interview Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Schedule and manage interviews for applicants.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => alert('Schedule new interview functionality would be implemented here')}
        >
          Schedule Interview
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <EventIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {interviews.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Interviews
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <EventIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    {interviews.filter(i => i.status === 'scheduled').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Scheduled
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <EventIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {interviews.filter(i => i.status === 'completed').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PersonIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {interviews.filter(i => i.score !== null).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Scored
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Interviews Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            All Interviews
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Applicant</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Date & Time</TableCell>
                  <TableCell>Duration</TableCell>
                  <TableCell>Location/Link</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Score</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {interviews.map((interview) => (
                  <TableRow key={interview.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {interview.applications.first_name} {interview.applications.last_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {interview.applications.application_number}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={interview.interview_type.toUpperCase()}
                        color={getInterviewTypeColor(interview.interview_type)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {new Date(interview.scheduled_date).toLocaleDateString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(interview.scheduled_date).toLocaleTimeString()}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {interview.duration_minutes} min
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {interview.meeting_link ? (
                          <>
                            <VideoCallIcon fontSize="small" color="primary" />
                            <Typography variant="body2" color="primary">
                              Online
                            </Typography>
                          </>
                        ) : (
                          <>
                            <LocationIcon fontSize="small" color="action" />
                            <Typography variant="body2">
                              {interview.location || 'TBD'}
                            </Typography>
                          </>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={interview.status.replace('_', ' ').toUpperCase()}
                        color={getStatusColor(interview.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {interview.score !== null ? `${interview.score}/10` : 'Not scored'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`View interview details for ${interview.applications.first_name}`)}
                        >
                          View
                        </Button>
                        {interview.status === 'scheduled' && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => alert(`Edit interview for ${interview.applications.first_name}`)}
                          >
                            Edit
                          </Button>
                        )}
                        {interview.status === 'completed' && interview.score === null && (
                          <Button
                            size="small"
                            variant="contained"
                            color="success"
                            onClick={() => alert(`Score interview for ${interview.applications.first_name}`)}
                          >
                            Score
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes. 
          In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Calendar integration for scheduling</li>
          <li>Video conferencing integration (Zoom, Teams, etc.)</li>
          <li>Automated email notifications and reminders</li>
          <li>Interview scoring and evaluation forms</li>
          <li>Panel member assignment and coordination</li>
          <li>Interview feedback and notes management</li>
        </ul>
      </Box>
    </Box>
  );
};

export default SimpleInterviewsPage;
