'use client';

import React from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  School as SchoolIcon,
  Add as AddIcon,
  Assignment as OfferIcon,
  CheckCircle as CheckIcon,
  AttachMoney as MoneyIcon,
  Person as PersonIcon,
  Class as ClassIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';

const EnrollmentsPage: React.FC = () => {
  // Enhanced dummy data for enrollments
  const enrollments = [
    {
      id: '1',
      student_id: 'STU2024001',
      enrollment_date: '2024-01-15',
      status: 'enrolled',
      fees_paid: 15000,
      total_fees: 20000,
      applications: {
        first_name: '<PERSON>',
        last_name: 'Doe',
        application_number: 'APP2024-001234'
      },
      classes: {
        class_name: 'CS-2024-A',
        academic_programs: {
          name: 'Bachelor of Computer Science'
        }
      },
      academic_years: {
        year_name: '2024-2025'
      }
    },
    {
      id: '2',
      student_id: 'STU2024002',
      enrollment_date: '2024-01-18',
      status: 'enrolled',
      fees_paid: 25000,
      total_fees: 25000,
      applications: {
        first_name: 'Jane',
        last_name: 'Smith',
        application_number: 'APP2024-001235'
      },
      classes: {
        class_name: 'MBA-2024-A',
        academic_programs: {
          name: 'Master of Business Administration'
        }
      },
      academic_years: {
        year_name: '2024-2025'
      }
    },
    {
      id: '3',
      student_id: 'STU2024003',
      enrollment_date: '2024-01-20',
      status: 'deferred',
      fees_paid: 5000,
      total_fees: 18000,
      applications: {
        first_name: 'Mike',
        last_name: 'Johnson',
        application_number: 'APP2024-001236'
      },
      classes: {
        class_name: 'ENG-2024-B',
        academic_programs: {
          name: 'Bachelor of Engineering'
        }
      },
      academic_years: {
        year_name: '2024-2025'
      }
    }
  ];

  const pendingOffers = [
    {
      id: '1',
      application_number: 'APP2024-001240',
      first_name: 'Sarah',
      last_name: 'Wilson',
      program_name: 'Master of Science',
      offer_date: '2024-01-20',
      deadline: '2024-02-15',
      status: 'offered'
    },
    {
      id: '2',
      application_number: 'APP2024-001241',
      first_name: 'David',
      last_name: 'Brown',
      program_name: 'Bachelor of Arts',
      offer_date: '2024-01-22',
      deadline: '2024-02-20',
      status: 'offered'
    }
  ];

  const getStatusColor = (status: string) => {
    const colors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      enrolled: 'success',
      deferred: 'warning',
      cancelled: 'error',
      graduated: 'info',
      transferred: 'secondary',
      withdrawn: 'default',
    };
    return colors[status] || 'default';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Enrollment Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage student enrollments and track academic progress.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => alert('Enroll new student functionality would be implemented here')}
        >
          Enroll Student
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SchoolIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="primary">
                    {enrollments.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Enrollments
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CheckIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="success.main">
                    {enrollments.filter(e => e.status === 'enrolled').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Students
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <MoneyIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="info.main">
                    ${enrollments.reduce((sum, e) => sum + e.fees_paid, 0).toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Fees Collected
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <OfferIcon color="warning" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" color="warning.main">
                    {pendingOffers.length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pending Offers
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Generate Offers
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Send offer letters to qualified applicants
              </Typography>
              <Button
                variant="contained"
                startIcon={<OfferIcon />}
                fullWidth
                onClick={() => alert('Generate offer letters functionality would be implemented here')}
              >
                Send Offer Letters
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Process Enrollments
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Enroll students who accepted offers
              </Typography>
              <Button
                variant="contained"
                startIcon={<SchoolIcon />}
                fullWidth
                onClick={() => alert('Process enrollments functionality would be implemented here')}
              >
                Process Enrollments
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Generate Reports
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Create enrollment and financial reports
              </Typography>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => alert('Generate reports functionality would be implemented here')}
              >
                Generate Reports
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Current Enrollments Table */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current Enrollments
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Student ID</TableCell>
                  <TableCell>Student Name</TableCell>
                  <TableCell>Program</TableCell>
                  <TableCell>Class</TableCell>
                  <TableCell>Enrollment Date</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Fees</TableCell>
                  <TableCell>Academic Year</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {enrollments.map((enrollment) => (
                  <TableRow key={enrollment.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="primary">
                        {enrollment.student_id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {enrollment.applications.first_name} {enrollment.applications.last_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {enrollment.applications.application_number}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {enrollment.classes.academic_programs.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={enrollment.classes.class_name}
                        size="small"
                        variant="outlined"
                        color="primary"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(enrollment.enrollment_date).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={enrollment.status.toUpperCase()}
                        color={getStatusColor(enrollment.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          ${enrollment.fees_paid.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          of ${enrollment.total_fees.toLocaleString()}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(enrollment.fees_paid / enrollment.total_fees) * 100}
                          sx={{ mt: 0.5, height: 4 }}
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {enrollment.academic_years.year_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`View enrollment details for ${enrollment.student_id}`)}
                        >
                          View
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`Edit enrollment for ${enrollment.student_id}`)}
                        >
                          Edit
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Pending Offers */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Pending Offers
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Application #</TableCell>
                  <TableCell>Applicant Name</TableCell>
                  <TableCell>Program</TableCell>
                  <TableCell>Offer Date</TableCell>
                  <TableCell>Deadline</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {pendingOffers.map((offer) => (
                  <TableRow key={offer.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold" color="primary">
                        {offer.application_number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {offer.first_name} {offer.last_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {offer.program_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(offer.offer_date).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="warning.main">
                        {new Date(offer.deadline).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label="OFFERED"
                        color="warning"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          variant="contained"
                          color="success"
                          onClick={() => alert(`Process enrollment for ${offer.first_name} ${offer.last_name}`)}
                        >
                          Enroll
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => alert(`Send reminder to ${offer.first_name} ${offer.last_name}`)}
                        >
                          Remind
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Demo Notice */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="body2">
          <strong>Demo Mode:</strong> This page displays dummy data for demonstration purposes.
          In a real implementation, this would include:
        </Typography>
        <ul>
          <li>Automated offer letter generation and delivery</li>
          <li>Online acceptance and enrollment forms</li>
          <li>Fee payment integration and tracking</li>
          <li>Student ID generation and management</li>
          <li>Class assignment and capacity management</li>
          <li>Academic calendar integration</li>
          <li>Student portal access provisioning</li>
        </ul>
      </Box>
    </Box>
  );
};

export default EnrollmentsPage;
