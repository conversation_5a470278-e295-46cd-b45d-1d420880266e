/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ccore%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22GitHubBanner%22%2C%22Refine%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ckbar%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineKbarProvider%22%2C%22RefineKbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cmui%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineSnackbarProvider%22%2C%22useNotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cnextjs-router%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccontexts%5C%5Ccolor-mode%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22ColorModeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider%5C%5Cauth-provider.client.ts%22%2C%22ids%22%3A%5B%22authProviderClient%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdata-provider%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22dataProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdevtools%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22DevtoolsProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ccore%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22GitHubBanner%22%2C%22Refine%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ckbar%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineKbarProvider%22%2C%22RefineKbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cmui%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineSnackbarProvider%22%2C%22useNotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cnextjs-router%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccontexts%5C%5Ccolor-mode%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22ColorModeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider%5C%5Cauth-provider.client.ts%22%2C%22ids%22%3A%5B%22authProviderClient%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdata-provider%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22dataProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdevtools%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22DevtoolsProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/core/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/core/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/kbar/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/kbar/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/mui/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@refinedev/nextjs-router/dist/index.mjs */ \"(ssr)/./node_modules/@refinedev/nextjs-router/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/color-mode/index.tsx */ \"(ssr)/./src/contexts/color-mode/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider/auth-provider.client.ts */ \"(ssr)/./src/providers/auth-provider/auth-provider.client.ts\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/data-provider/index.ts */ \"(ssr)/./src/providers/data-provider/index.ts\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/devtools/index.tsx */ \"(ssr)/./src/providers/devtools/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ccore%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22GitHubBanner%22%2C%22Refine%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Ckbar%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineKbarProvider%22%2C%22RefineKbar%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cmui%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22RefineSnackbarProvider%22%2C%22useNotificationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5C%40refinedev%5C%5Cnextjs-router%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccontexts%5C%5Ccolor-mode%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22ColorModeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider%5C%5Cauth-provider.client.ts%22%2C%22ids%22%3A%5B%22authProviderClient%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdata-provider%5C%5Cindex.ts%22%2C%22ids%22%3A%5B%22dataProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Cproviders%5C%5Cdevtools%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22DevtoolsProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbm90LWZvdW5kLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQTJHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8/Njg3ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXGNpdHJ1cy13b3Jrc1xcXFxDTVNcXFxcc3R1ZGVudC1hZG1pc3Npb25zLWNtc1xcXFxzcmNcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-page%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AuthPage%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-page%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AuthPage%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-page/index.tsx */ \"(ssr)/./src/components/auth-page/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNjaXRydXMtd29ya3MlNUMlNUNDTVMlNUMlNUNzdHVkZW50LWFkbWlzc2lvbnMtY21zJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2F1dGgtcGFnZSU1QyU1Q2luZGV4LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQYWdlJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBdUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLz9iNzVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFBhZ2VcIl0gKi8gXCJDOlxcXFxjaXRydXMtd29ya3NcXFxcQ01TXFxcXHN0dWRlbnQtYWRtaXNzaW9ucy1jbXNcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcYXV0aC1wYWdlXFxcXGluZGV4LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Ccitrus-works%5C%5CCMS%5C%5Cstudent-admissions-cms%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-page%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AuthPage%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @refinedev/mui */ \"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* harmony import */ var _refinedev_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @refinedev/core */ \"(ssr)/./node_modules/@refinedev/core/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_core__WEBPACK_IMPORTED_MODULE_2__.Authenticated, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_mui__WEBPACK_IMPORTED_MODULE_3__.ErrorComponent, {}, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 11,\n                columnNumber: 17\n            }, this)\n        }, \"not-found\", false, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 10,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 9,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFZ0M7QUFDb0I7QUFDTDtBQUVoQyxTQUFTRztJQUNwQixxQkFDSSw4REFBQ0gsMkNBQVFBO2tCQUNMLDRFQUFDRSwwREFBYUE7c0JBQ1YsNEVBQUNELDBEQUFjQTs7Ozs7V0FEQTs7Ozs7Ozs7OztBQUsvQiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9zcmMvYXBwL25vdC1mb3VuZC50c3g/Y2FlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyBTdXNwZW5zZSB9IGZyb20gJ3JlYWN0J1xuICAgIGltcG9ydCB7IEVycm9yQ29tcG9uZW50IH0gZnJvbSBcIkByZWZpbmVkZXYvbXVpXCI7XG5pbXBvcnQgeyBBdXRoZW50aWNhdGVkIH0gZnJvbSAnQHJlZmluZWRldi9jb3JlJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgICByZXR1cm4gKFxuICAgICAgICA8U3VzcGVuc2U+XG4gICAgICAgICAgICA8QXV0aGVudGljYXRlZCBrZXk9J25vdC1mb3VuZCc+XG4gICAgICAgICAgICAgICAgPEVycm9yQ29tcG9uZW50IC8+XG4gICAgICAgICAgICA8L0F1dGhlbnRpY2F0ZWQ+XG4gICAgICAgIDwvU3VzcGVuc2U+XG4gICAgKVxufVxuIl0sIm5hbWVzIjpbIlN1c3BlbnNlIiwiRXJyb3JDb21wb25lbnQiLCJBdXRoZW50aWNhdGVkIiwiTm90Rm91bmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth-page/index.tsx":
/*!********************************************!*\
  !*** ./src/components/auth-page/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthPage: () => (/* binding */ AuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @refinedev/mui */ \"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthPage auto */ \n\nconst AuthPage = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_mui__WEBPACK_IMPORTED_MODULE_1__.AuthPage, {\n        ...props,\n        formProps: {\n            defaultValues: {\n                email: \"<EMAIL>\",\n                password: \"refine-supabase\"\n            }\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\components\\\\auth-page\\\\index.tsx\",\n        lineNumber: 11,\n        columnNumber: 11\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth-page/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/color-mode/index.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/color-mode/index.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorModeContext: () => (/* binding */ ColorModeContext),\n/* harmony export */   ColorModeContextProvider: () => (/* binding */ ColorModeContextProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/useMediaQuery */ \"(ssr)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material/styles */ \"(ssr)/./node_modules/@mui/material/styles/ThemeProvider.js\");\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @refinedev/mui */ \"(ssr)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _mui_material_GlobalStyles__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/GlobalStyles */ \"(ssr)/./node_modules/@mui/material/GlobalStyles/GlobalStyles.js\");\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(ssr)/./node_modules/@mui/material/CssBaseline/CssBaseline.js\");\n/* __next_internal_client_entry_do_not_use__ ColorModeContext,ColorModeContextProvider auto */ \n\n\n\n\n\n\n\nconst ColorModeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nconst ColorModeContextProvider = ({ children, defaultMode })=>{\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultMode || \"light\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    const systemTheme = (0,_mui_material_useMediaQuery__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(`(prefers-color-scheme: dark)`);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMounted) {\n            const theme = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"theme\") || (systemTheme ? \"dark\" : \"light\");\n            setMode(theme);\n        }\n    }, [\n        isMounted,\n        systemTheme\n    ]);\n    const toggleTheme = ()=>{\n        const nextTheme = mode === \"light\" ? \"dark\" : \"light\";\n        setMode(nextTheme);\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"theme\", nextTheme);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ColorModeContext.Provider, {\n        value: {\n            setMode: toggleTheme,\n            mode\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            // you can change the theme colors here. example: mode === \"light\" ? RefineThemes.Magenta : RefineThemes.MagentaDark\n            theme: mode === \"light\" ? _refinedev_mui__WEBPACK_IMPORTED_MODULE_5__.RefineThemes.Blue : _refinedev_mui__WEBPACK_IMPORTED_MODULE_5__.RefineThemes.BlueDark,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GlobalStyles__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    styles: {\n                        html: {\n                            WebkitFontSmoothing: \"auto\"\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 17\n                }, undefined),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n            lineNumber: 62,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\contexts\\\\color-mode\\\\index.tsx\",\n        lineNumber: 56,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/color-mode/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/auth-provider/auth-provider.client.ts":
/*!*************************************************************!*\
  !*** ./src/providers/auth-provider/auth-provider.client.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authProviderClient: () => (/* binding */ authProviderClient)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ authProviderClient auto */ \nconst authProviderClient = {\n    login: async ({ email, password })=>{\n        const { data, error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            return {\n                success: false,\n                error\n            };\n        }\n        if (data?.session) {\n            await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.setSession(data.session);\n            return {\n                success: true,\n                redirectTo: \"/\"\n            };\n        }\n        // for third-party login\n        return {\n            success: false,\n            error: {\n                name: \"LoginError\",\n                message: \"Invalid username or password\"\n            }\n        };\n    },\n    logout: async ()=>{\n        const { error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.signOut();\n        if (error) {\n            return {\n                success: false,\n                error\n            };\n        }\n        return {\n            success: true,\n            redirectTo: \"/login\"\n        };\n    },\n    register: async ({ email, password, fullName, role = \"applicant\" })=>{\n        try {\n            const { data, error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: fullName,\n                        role: role\n                    }\n                }\n            });\n            if (error) {\n                return {\n                    success: false,\n                    error\n                };\n            }\n            if (data?.user) {\n                // Create user profile in public.users table\n                const { error: profileError } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.from(\"users\").insert({\n                    id: data.user.id,\n                    email: data.user.email,\n                    full_name: fullName,\n                    role: role\n                });\n                if (profileError) {\n                    console.error(\"Error creating user profile:\", profileError);\n                }\n                return {\n                    success: true,\n                    redirectTo: role === \"applicant\" ? \"/applications\" : \"/dashboard\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                error\n            };\n        }\n        return {\n            success: false,\n            error: {\n                message: \"Register failed\",\n                name: \"Invalid email or password\"\n            }\n        };\n    },\n    check: async ()=>{\n        const { data, error } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.getUser();\n        const { user } = data;\n        if (error) {\n            return {\n                authenticated: false,\n                redirectTo: \"/login\",\n                logout: true\n            };\n        }\n        if (user) {\n            return {\n                authenticated: true\n            };\n        }\n        return {\n            authenticated: false,\n            redirectTo: \"/login\"\n        };\n    },\n    getPermissions: async ()=>{\n        const { data } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.getUser();\n        if (data?.user) {\n            // Get user role from public.users table\n            const { data: userProfile } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.from(\"users\").select(\"role\").eq(\"id\", data.user.id).single();\n            return userProfile?.role || \"applicant\";\n        }\n        return null;\n    },\n    getIdentity: async ()=>{\n        const { data } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.auth.getUser();\n        if (data?.user) {\n            // Get full user profile from public.users table\n            const { data: userProfile } = await _utils_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabaseBrowserClient.from(\"users\").select(\"*\").eq(\"id\", data.user.id).single();\n            return {\n                id: data.user.id,\n                email: data.user.email,\n                name: userProfile?.full_name || data.user.email,\n                avatar: userProfile?.avatar_url,\n                role: userProfile?.role || \"applicant\",\n                phone: userProfile?.phone,\n                isActive: userProfile?.is_active\n            };\n        }\n        return null;\n    },\n    onError: async (error)=>{\n        if (error?.code === \"PGRST301\" || error?.code === 401) {\n            return {\n                logout: true\n            };\n        }\n        return {\n            error\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/auth-provider/auth-provider.client.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/data-provider/index.ts":
/*!**********************************************!*\
  !*** ./src/providers/data-provider/index.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataProvider: () => (/* binding */ dataProvider)\n/* harmony export */ });\n/* harmony import */ var _refinedev_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @refinedev/supabase */ \"(ssr)/./node_modules/@refinedev/supabase/dist/index.mjs\");\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ dataProvider auto */ \n\nconst dataProvider = (0,_refinedev_supabase__WEBPACK_IMPORTED_MODULE_0__.dataProvider)(_utils_supabase_client__WEBPACK_IMPORTED_MODULE_1__.supabaseBrowserClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL2RhdGEtcHJvdmlkZXIvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O2tFQUUyRTtBQUNaO0FBRXhELE1BQU1BLGVBQWVDLGlFQUFvQkEsQ0FBQ0MseUVBQXFCQSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3R1ZGVudC1hZG1pc3Npb25zLWNtcy8uL3NyYy9wcm92aWRlcnMvZGF0YS1wcm92aWRlci9pbmRleC50cz8zYTIzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBkYXRhUHJvdmlkZXIgYXMgZGF0YVByb3ZpZGVyU3VwYWJhc2UgfSBmcm9tIFwiQHJlZmluZWRldi9zdXBhYmFzZVwiO1xuaW1wb3J0IHsgc3VwYWJhc2VCcm93c2VyQ2xpZW50IH0gZnJvbSBcIkB1dGlscy9zdXBhYmFzZS9jbGllbnRcIjtcblxuZXhwb3J0IGNvbnN0IGRhdGFQcm92aWRlciA9IGRhdGFQcm92aWRlclN1cGFiYXNlKHN1cGFiYXNlQnJvd3NlckNsaWVudCk7XG4iXSwibmFtZXMiOlsiZGF0YVByb3ZpZGVyIiwiZGF0YVByb3ZpZGVyU3VwYWJhc2UiLCJzdXBhYmFzZUJyb3dzZXJDbGllbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/data-provider/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/devtools/index.tsx":
/*!******************************************!*\
  !*** ./src/providers/devtools/index.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DevtoolsProvider: () => (/* binding */ DevtoolsProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _refinedev_devtools__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @refinedev/devtools */ \"(ssr)/./node_modules/@refinedev/devtools/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ DevtoolsProvider auto */ \n\n\nconst DevtoolsProvider = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_devtools__WEBPACK_IMPORTED_MODULE_2__.DevtoolsProvider, {\n        children: [\n            props.children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_devtools__WEBPACK_IMPORTED_MODULE_2__.DevtoolsPanel, {}, void 0, false, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\providers\\\\devtools\\\\index.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\providers\\\\devtools\\\\index.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL2RldnRvb2xzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXlCO0FBQ29FO0FBRXRGLE1BQU1FLG1CQUFtQixDQUFDRTtJQUMvQixxQkFDRSw4REFBQ0QsaUVBQW9CQTs7WUFDbEJDLE1BQU1DLFFBQVE7MEJBQ2YsOERBQUNKLDhEQUFhQTs7Ozs7Ozs7Ozs7QUFHcEIsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9zcmMvcHJvdmlkZXJzL2RldnRvb2xzL2luZGV4LnRzeD9jMzRmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBEZXZ0b29sc1BhbmVsLCBEZXZ0b29sc1Byb3ZpZGVyIGFzIERldnRvb2xzUHJvdmlkZXJCYXNlIH0gZnJvbSAnQHJlZmluZWRldi9kZXZ0b29scydcblxuZXhwb3J0IGNvbnN0IERldnRvb2xzUHJvdmlkZXIgPSAocHJvcHM6IFJlYWN0LlByb3BzV2l0aENoaWxkcmVuKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPERldnRvb2xzUHJvdmlkZXJCYXNlPlxuICAgICAge3Byb3BzLmNoaWxkcmVufVxuICAgICAgPERldnRvb2xzUGFuZWwgLz5cbiAgICA8L0RldnRvb2xzUHJvdmlkZXJCYXNlPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJEZXZ0b29sc1BhbmVsIiwiRGV2dG9vbHNQcm92aWRlciIsIkRldnRvb2xzUHJvdmlkZXJCYXNlIiwicHJvcHMiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/devtools/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/supabase/client.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/client.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabaseBrowserClient: () => (/* binding */ supabaseBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/./src/utils/supabase/constants.ts\");\n\n\nconst supabaseBrowserClient = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(_constants__WEBPACK_IMPORTED_MODULE_1__.SUPABASE_URL, _constants__WEBPACK_IMPORTED_MODULE_1__.SUPABASE_KEY, {\n    db: {\n        schema: \"public\"\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUNLO0FBRWxELE1BQU1HLHdCQUF3Qkgsa0VBQW1CQSxDQUNwREUsb0RBQVlBLEVBQ1pELG9EQUFZQSxFQUNaO0lBQ0lHLElBQUk7UUFDQUMsUUFBUTtJQUNaO0FBQ0osR0FDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3N0dWRlbnQtYWRtaXNzaW9ucy1jbXMvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzPzcxYTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQnJvd3NlckNsaWVudCB9IGZyb20gXCJAc3VwYWJhc2Uvc3NyXCI7XG5pbXBvcnQgeyBTVVBBQkFTRV9LRVksIFNVUEFCQVNFX1VSTCB9IGZyb20gXCIuL2NvbnN0YW50c1wiO1xuXG5leHBvcnQgY29uc3Qgc3VwYWJhc2VCcm93c2VyQ2xpZW50ID0gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBTVVBBQkFTRV9VUkwsXG4gICAgU1VQQUJBU0VfS0VZLFxuICAgIHtcbiAgICAgICAgZGI6IHtcbiAgICAgICAgICAgIHNjaGVtYTogXCJwdWJsaWNcIixcbiAgICAgICAgfSxcbiAgICB9LFxuKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiU1VQQUJBU0VfS0VZIiwiU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VCcm93c2VyQ2xpZW50IiwiZGIiLCJzY2hlbWEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/supabase/constants.ts":
/*!*****************************************!*\
  !*** ./src/utils/supabase/constants.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPABASE_KEY: () => (/* binding */ SUPABASE_KEY),\n/* harmony export */   SUPABASE_URL: () => (/* binding */ SUPABASE_URL)\n/* harmony export */ });\nconst SUPABASE_URL = \"https://iwdfzvfqbtokqetmbmbp.supabase.co\";\nconst SUPABASE_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************._gr6kXGkQBi9BM9dx5vKaNKYj_DJN1xlkarprGpM_fU\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY29uc3RhbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sTUFBTUEsZUFBZSwyQ0FBMkM7QUFDaEUsTUFBTUMsZUFDVCxzSkFBc0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vc3JjL3V0aWxzL3N1cGFiYXNlL2NvbnN0YW50cy50cz81MDY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBTVVBBQkFTRV9VUkwgPSBcImh0dHBzOi8vaXdkZnp2ZnFidG9rcWV0bWJtYnAuc3VwYWJhc2UuY29cIjtcbmV4cG9ydCBjb25zdCBTVVBBQkFTRV9LRVkgPVxuICAgIFwiZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnliMnhsSWpvaVlXNXZiaUlzSW1saGRDSTZNVFl6TURVMk56QXhNQ3dpWlhod0lqb3hPVFEyTVRRek1ERXdmUS5fZ3I2a1hHa1FCaTlCTTlkeDV2S2FOS1lqX0RKTjF4bGthcnByR3BNX2ZVXCI7XG4iXSwibmFtZXMiOlsiU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfS0VZIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/supabase/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _refinedev_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @refinedev/core */ \"(rsc)/./node_modules/@refinedev/core/dist/index.mjs\");\n/* harmony import */ var _providers_devtools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @providers/devtools */ \"(rsc)/./src/providers/devtools/index.tsx\");\n/* harmony import */ var _refinedev_kbar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @refinedev/kbar */ \"(rsc)/./node_modules/@refinedev/kbar/dist/index.mjs\");\n/* harmony import */ var _refinedev_mui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @refinedev/mui */ \"(rsc)/./node_modules/@refinedev/mui/dist/index.mjs\");\n/* harmony import */ var _refinedev_nextjs_router__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @refinedev/nextjs-router */ \"(rsc)/./node_modules/@refinedev/nextjs-router/dist/index.mjs\");\n/* harmony import */ var _providers_auth_provider_auth_provider_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @providers/auth-provider/auth-provider.client */ \"(rsc)/./src/providers/auth-provider/auth-provider.client.ts\");\n/* harmony import */ var _providers_data_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @providers/data-provider */ \"(rsc)/./src/providers/data-provider/index.ts\");\n/* harmony import */ var _contexts_color_mode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @contexts/color-mode */ \"(rsc)/./src/contexts/color-mode/index.tsx\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Refine\",\n    description: \"Generated by create refine app\",\n    icons: {\n        icon: \"/favicon.ico\"\n    }\n};\nfunction RootLayout({ children }) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    const theme = cookieStore.get(\"theme\");\n    const defaultMode = theme?.value === \"dark\" ? \"dark\" : \"light\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_core__WEBPACK_IMPORTED_MODULE_7__.GitHubBanner, {}, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_kbar__WEBPACK_IMPORTED_MODULE_8__.RefineKbarProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_color_mode__WEBPACK_IMPORTED_MODULE_6__.ColorModeContextProvider, {\n                            defaultMode: defaultMode,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_mui__WEBPACK_IMPORTED_MODULE_9__.RefineSnackbarProvider, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_devtools__WEBPACK_IMPORTED_MODULE_3__.DevtoolsProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_core__WEBPACK_IMPORTED_MODULE_7__.Refine, {\n                                        routerProvider: _refinedev_nextjs_router__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                        authProvider: _providers_auth_provider_auth_provider_client__WEBPACK_IMPORTED_MODULE_4__.authProviderClient,\n                                        dataProvider: _providers_data_provider__WEBPACK_IMPORTED_MODULE_5__.dataProvider,\n                                        notificationProvider: _refinedev_mui__WEBPACK_IMPORTED_MODULE_9__.useNotificationProvider,\n                                        resources: [\n                                            {\n                                                name: \"inquiries\",\n                                                list: \"/inquiries\",\n                                                create: \"/inquiries/create\",\n                                                edit: \"/inquiries/edit/:id\",\n                                                show: \"/inquiries/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Inquiries\",\n                                                    icon: \"contact_support\"\n                                                }\n                                            },\n                                            {\n                                                name: \"applications\",\n                                                list: \"/applications\",\n                                                create: \"/apply\",\n                                                edit: \"/applications/edit/:id\",\n                                                show: \"/applications/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Applications\",\n                                                    icon: \"assignment\"\n                                                }\n                                            },\n                                            {\n                                                name: \"interviews\",\n                                                list: \"/interviews\",\n                                                create: \"/interviews/create\",\n                                                edit: \"/interviews/edit/:id\",\n                                                show: \"/interviews/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Interviews\",\n                                                    icon: \"event\"\n                                                }\n                                            },\n                                            {\n                                                name: \"enrollments\",\n                                                list: \"/enrollments\",\n                                                create: \"/enrollments/create\",\n                                                edit: \"/enrollments/edit/:id\",\n                                                show: \"/enrollments/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Enrollments\",\n                                                    icon: \"school\"\n                                                }\n                                            },\n                                            {\n                                                name: \"capacity\",\n                                                list: \"/capacity\",\n                                                meta: {\n                                                    label: \"Capacity Management\",\n                                                    icon: \"group\"\n                                                }\n                                            },\n                                            {\n                                                name: \"group-capacity\",\n                                                list: \"/group-capacity\",\n                                                meta: {\n                                                    label: \"Group Capacity\",\n                                                    icon: \"groups\"\n                                                }\n                                            },\n                                            {\n                                                name: \"communications\",\n                                                list: \"/communications\",\n                                                create: \"/communications/create\",\n                                                edit: \"/communications/edit/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Communications\",\n                                                    icon: \"email\"\n                                                }\n                                            },\n                                            {\n                                                name: \"reports\",\n                                                list: \"/reports\",\n                                                meta: {\n                                                    label: \"Reports\",\n                                                    icon: \"analytics\"\n                                                }\n                                            },\n                                            {\n                                                name: \"blog_posts\",\n                                                list: \"/blog-posts\",\n                                                create: \"/blog-posts/create\",\n                                                edit: \"/blog-posts/edit/:id\",\n                                                show: \"/blog-posts/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Blog Posts\",\n                                                    icon: \"article\"\n                                                }\n                                            },\n                                            {\n                                                name: \"categories\",\n                                                list: \"/categories\",\n                                                create: \"/categories/create\",\n                                                edit: \"/categories/edit/:id\",\n                                                show: \"/categories/show/:id\",\n                                                meta: {\n                                                    canDelete: true,\n                                                    label: \"Categories\",\n                                                    icon: \"category\"\n                                                }\n                                            }\n                                        ],\n                                        options: {\n                                            syncWithLocation: true,\n                                            warnWhenUnsavedChanges: true,\n                                            useNewQueryKeys: true,\n                                            projectId: \"7BvQym-ncceep-txvsN1\"\n                                        },\n                                        children: [\n                                            children,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_refinedev_kbar__WEBPACK_IMPORTED_MODULE_8__.RefineKbar, {}, void 0, false, {\n                                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 1\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 41,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 40,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Login)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_auth_page__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @components/auth-page */ \"(rsc)/./src/components/auth-page/index.tsx\");\n/* harmony import */ var _providers_auth_provider_auth_provider_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @providers/auth-provider/auth-provider.server */ \"(rsc)/./src/providers/auth-provider/auth-provider.server.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n\n\n\nasync function Login() {\n    const data = await getData();\n    if (data.authenticated) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.redirect)(data?.redirectTo || \"/\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_page__WEBPACK_IMPORTED_MODULE_1__.AuthPage, {\n        type: \"login\"\n    }, void 0, false, {\n        fileName: \"C:\\\\citrus-works\\\\CMS\\\\student-admissions-cms\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 12\n    }, this);\n}\nasync function getData() {\n    const { authenticated, redirectTo, error } = await _providers_auth_provider_auth_provider_server__WEBPACK_IMPORTED_MODULE_2__.authProviderServer.check();\n    return {\n        authenticated,\n        redirectTo,\n        error\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvZ2luL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBaUQ7QUFDa0M7QUFDeEM7QUFFNUIsZUFBZUc7SUFDMUIsTUFBTUMsT0FBTyxNQUFNQztJQUVuQixJQUFJRCxLQUFLRSxhQUFhLEVBQUU7UUFDcEJKLHlEQUFRQSxDQUFDRSxNQUFNRyxjQUFjO0lBQ2pDO0lBRUEscUJBQU8sOERBQUNQLDJEQUFRQTtRQUFDUSxNQUFLOzs7Ozs7QUFDMUI7QUFFQSxlQUFlSDtJQUNYLE1BQU0sRUFBRUMsYUFBYSxFQUFFQyxVQUFVLEVBQUVFLEtBQUssRUFBRSxHQUN0QyxNQUFNUiw2RkFBa0JBLENBQUNTLEtBQUs7SUFFbEMsT0FBTztRQUNISjtRQUNBQztRQUNBRTtJQUNKO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vc3JjL2FwcC9sb2dpbi9wYWdlLnRzeD9mYzYzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEF1dGhQYWdlIH0gZnJvbSBcIkBjb21wb25lbnRzL2F1dGgtcGFnZVwiO1xuaW1wb3J0IHsgYXV0aFByb3ZpZGVyU2VydmVyIH0gZnJvbSBcIkBwcm92aWRlcnMvYXV0aC1wcm92aWRlci9hdXRoLXByb3ZpZGVyLnNlcnZlclwiO1xuaW1wb3J0IHsgcmVkaXJlY3QgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIExvZ2luKCkge1xuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBnZXREYXRhKCk7XG5cbiAgICBpZiAoZGF0YS5hdXRoZW50aWNhdGVkKSB7XG4gICAgICAgIHJlZGlyZWN0KGRhdGE/LnJlZGlyZWN0VG8gfHwgXCIvXCIpO1xuICAgIH1cblxuICAgIHJldHVybiA8QXV0aFBhZ2UgdHlwZT1cImxvZ2luXCIgLz47XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGdldERhdGEoKSB7XG4gICAgY29uc3QgeyBhdXRoZW50aWNhdGVkLCByZWRpcmVjdFRvLCBlcnJvciB9ID1cbiAgICAgICAgYXdhaXQgYXV0aFByb3ZpZGVyU2VydmVyLmNoZWNrKCk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgICBhdXRoZW50aWNhdGVkLFxuICAgICAgICByZWRpcmVjdFRvLFxuICAgICAgICBlcnJvcixcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbIkF1dGhQYWdlIiwiYXV0aFByb3ZpZGVyU2VydmVyIiwicmVkaXJlY3QiLCJMb2dpbiIsImRhdGEiLCJnZXREYXRhIiwiYXV0aGVudGljYXRlZCIsInJlZGlyZWN0VG8iLCJ0eXBlIiwiZXJyb3IiLCJjaGVjayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\app\not-found.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/auth-page/index.tsx":
/*!********************************************!*\
  !*** ./src/components/auth-page/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthPage: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\components\auth-page\index.tsx#AuthPage`);


/***/ }),

/***/ "(rsc)/./src/contexts/color-mode/index.tsx":
/*!*******************************************!*\
  !*** ./src/contexts/color-mode/index.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ColorModeContext: () => (/* binding */ e0),
/* harmony export */   ColorModeContextProvider: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\contexts\color-mode\index.tsx#ColorModeContext`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\contexts\color-mode\index.tsx#ColorModeContextProvider`);


/***/ }),

/***/ "(rsc)/./src/providers/auth-provider/auth-provider.client.ts":
/*!*************************************************************!*\
  !*** ./src/providers/auth-provider/auth-provider.client.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   authProviderClient: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\providers\auth-provider\auth-provider.client.ts#authProviderClient`);


/***/ }),

/***/ "(rsc)/./src/providers/auth-provider/auth-provider.server.ts":
/*!*************************************************************!*\
  !*** ./src/providers/auth-provider/auth-provider.server.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authProviderServer: () => (/* binding */ authProviderServer)\n/* harmony export */ });\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n\nconst authProviderServer = {\n    check: async ()=>{\n        const { data, error } = await (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_0__.createSupabaseServerClient)().auth.getUser();\n        const { user } = data;\n        if (error) {\n            return {\n                authenticated: false,\n                logout: true,\n                redirectTo: \"/login\"\n            };\n        }\n        if (user) {\n            return {\n                authenticated: true\n            };\n        }\n        return {\n            authenticated: false,\n            logout: true,\n            redirectTo: \"/login\"\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/providers/auth-provider/auth-provider.server.ts\n");

/***/ }),

/***/ "(rsc)/./src/providers/data-provider/index.ts":
/*!**********************************************!*\
  !*** ./src/providers/data-provider/index.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   dataProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\providers\data-provider\index.ts#dataProvider`);


/***/ }),

/***/ "(rsc)/./src/providers/devtools/index.tsx":
/*!******************************************!*\
  !*** ./src/providers/devtools/index.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DevtoolsProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\citrus-works\CMS\student-admissions-cms\src\providers\devtools\index.tsx#DevtoolsProvider`);


/***/ }),

/***/ "(rsc)/./src/utils/supabase/constants.ts":
/*!*****************************************!*\
  !*** ./src/utils/supabase/constants.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPABASE_KEY: () => (/* binding */ SUPABASE_KEY),\n/* harmony export */   SUPABASE_URL: () => (/* binding */ SUPABASE_URL)\n/* harmony export */ });\nconst SUPABASE_URL = \"https://iwdfzvfqbtokqetmbmbp.supabase.co\";\nconst SUPABASE_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************._gr6kXGkQBi9BM9dx5vKaNKYj_DJN1xlkarprGpM_fU\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY29uc3RhbnRzLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU8sTUFBTUEsZUFBZSwyQ0FBMkM7QUFDaEUsTUFBTUMsZUFDVCxzSkFBc0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vc3JjL3V0aWxzL3N1cGFiYXNlL2NvbnN0YW50cy50cz81MDY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBTVVBBQkFTRV9VUkwgPSBcImh0dHBzOi8vaXdkZnp2ZnFidG9rcWV0bWJtYnAuc3VwYWJhc2UuY29cIjtcbmV4cG9ydCBjb25zdCBTVVBBQkFTRV9LRVkgPVxuICAgIFwiZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SnliMnhsSWpvaVlXNXZiaUlzSW1saGRDSTZNVFl6TURVMk56QXhNQ3dpWlhod0lqb3hPVFEyTVRRek1ERXdmUS5fZ3I2a1hHa1FCaTlCTTlkeDV2S2FOS1lqX0RKTjF4bGthcnByR3BNX2ZVXCI7XG4iXSwibmFtZXMiOlsiU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfS0VZIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/server.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/server.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClient: () => (/* binding */ createSupabaseServerClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./src/utils/supabase/constants.ts\");\n\n\n\nconst createSupabaseServerClient = ()=>{\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(_constants__WEBPACK_IMPORTED_MODULE_2__.SUPABASE_URL, _constants__WEBPACK_IMPORTED_MODULE_2__.SUPABASE_KEY, {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                // The `set` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            },\n            remove (name, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value: \"\",\n                        ...options\n                    });\n                } catch (error) {\n                // The `delete` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__ ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"48x48\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"icon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"?7391c51acd569043\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2ljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdHVkZW50LWFkbWlzc2lvbnMtY21zLy4vc3JjL2FwcC9pY29uLmljbz8wMzY4Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjQ4eDQ4XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiaWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiPzczOTFjNTFhY2Q1NjkwNDNcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/icon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@mui","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/@refinedev","vendor-chunks/ws","vendor-chunks/micromark","vendor-chunks/lodash-es","vendor-chunks/@tanstack","vendor-chunks/react-hook-form","vendor-chunks/@emotion","vendor-chunks/whatwg-url","vendor-chunks/kbar","vendor-chunks/@popperjs","vendor-chunks/papaparse","vendor-chunks/fuse.js","vendor-chunks/notistack","vendor-chunks/prop-types","vendor-chunks/qs","vendor-chunks/property-information","vendor-chunks/react-transition-group","vendor-chunks/character-entities","vendor-chunks/react-markdown","vendor-chunks/stylis","vendor-chunks/mdast-util-from-markdown","vendor-chunks/object-inspect","vendor-chunks/mdast-util-to-hast","vendor-chunks/@radix-ui","vendor-chunks/ramda","vendor-chunks/get-intrinsic","vendor-chunks/react-virtual","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/pluralize","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/cookie","vendor-chunks/unified","vendor-chunks/fast-equals","vendor-chunks/webidl-conversions","vendor-chunks/hoist-non-react-statics","vendor-chunks/error-stack-parser","vendor-chunks/mdast-util-to-markdown","vendor-chunks/dayjs","vendor-chunks/markdown-table","vendor-chunks/vfile","vendor-chunks/inline-style-parser","vendor-chunks/react-is","vendor-chunks/stackframe","vendor-chunks/mdast-util-gfm-table","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/use-sync-external-store","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/js-cookie","vendor-chunks/side-channel-list","vendor-chunks/extend","vendor-chunks/trough","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/mdurl","vendor-chunks/function-bind","vendor-chunks/unist-util-visit-parents","vendor-chunks/object-assign","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/side-channel-map","vendor-chunks/vfile-message","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/@babel","vendor-chunks/mdast-util-gfm","vendor-chunks/isows","vendor-chunks/unist-util-is","vendor-chunks/repeat-string","vendor-chunks/side-channel","vendor-chunks/comma-separated-tokens","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/mdast-util-definitions","vendor-chunks/remark-gfm","vendor-chunks/remark-rehype","vendor-chunks/dunder-proto","vendor-chunks/style-to-object","vendor-chunks/@aliemir","vendor-chunks/unist-util-stringify-position","vendor-chunks/math-intrinsics","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/call-bound","vendor-chunks/unist-util-visit","vendor-chunks/mdast-util-to-string","vendor-chunks/remark-parse","vendor-chunks/unist-util-position","vendor-chunks/es-errors","vendor-chunks/micromark-extension-gfm","vendor-chunks/tiny-invariant","vendor-chunks/escape-string-regexp","vendor-chunks/unist-builder","vendor-chunks/clsx","vendor-chunks/xtend","vendor-chunks/ccount","vendor-chunks/space-separated-tokens","vendor-chunks/warn-once","vendor-chunks/unist-util-generated","vendor-chunks/gopd","vendor-chunks/is-buffer","vendor-chunks/es-define-property","vendor-chunks/parse-entities","vendor-chunks/is-plain-obj","vendor-chunks/hasown","vendor-chunks/bail","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Ccitrus-works%5CCMS%5Cstudent-admissions-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();